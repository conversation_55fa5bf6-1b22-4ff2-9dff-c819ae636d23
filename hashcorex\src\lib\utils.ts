import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

export function formatNumber(num: number, decimals = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num);
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(d);
}

export function formatDateTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(d);
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

export function generateId(): string {
  // Use crypto.randomUUID if available (modern browsers), fallback to timestamp + random
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID().replace(/-/g, '').substring(0, 9);
  }

  // Fallback for older browsers or server-side
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substr(2, 5);
  return (timestamp + randomPart).substr(0, 9);
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function copyToClipboard(text: string): Promise<void> {
  if (navigator.clipboard) {
    return navigator.clipboard.writeText(text);
  }
  
  // Fallback for older browsers
  const textArea = document.createElement('textarea');
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  
  try {
    document.execCommand('copy');
    return Promise.resolve();
  } catch (err) {
    return Promise.reject(err);
  } finally {
    document.body.removeChild(textArea);
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
  checks: Array<{ valid: boolean; message: string }>;
} {
  const errors: string[] = [];
  const checks: Array<{ valid: boolean; message: string }> = [];

  // Check length
  const lengthValid = password.length >= 8;
  checks.push({
    valid: lengthValid,
    message: 'At least 8 characters long'
  });
  if (!lengthValid) {
    errors.push('Password must be at least 8 characters long');
  }

  // Check uppercase
  const uppercaseValid = /[A-Z]/.test(password);
  checks.push({
    valid: uppercaseValid,
    message: 'At least one uppercase letter'
  });
  if (!uppercaseValid) {
    errors.push('Password must contain at least one uppercase letter');
  }

  // Check lowercase
  const lowercaseValid = /[a-z]/.test(password);
  checks.push({
    valid: lowercaseValid,
    message: 'At least one lowercase letter'
  });
  if (!lowercaseValid) {
    errors.push('Password must contain at least one lowercase letter');
  }

  // Check number
  const numberValid = /\d/.test(password);
  checks.push({
    valid: numberValid,
    message: 'At least one number'
  });
  if (!numberValid) {
    errors.push('Password must contain at least one number');
  }

  // Check special character
  const specialValid = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  checks.push({
    valid: specialValid,
    message: 'At least one special character'
  });
  if (!specialValid) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
    checks,
  };
}

export function calculateROI(
  investment: number,
  dailyRate: number,
  days: number
): number {
  return investment * (dailyRate / 100) * days;
}

export function calculateTHSPrice(ths: number, pricePerTHS: number): number {
  return ths * pricePerTHS;
}

export function formatTHS(ths: number): string {
  if (ths >= 1000) {
    return `${(ths / 1000).toFixed(1)}K TH/s`;
  }
  return `${ths.toFixed(2)} TH/s`;
}

export function getTimeUntilNextPayout(): {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
} {
  const now = new Date();
  const nextSaturday = new Date();
  
  // Set to next Saturday at 15:00 UTC
  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));
  nextSaturday.setUTCHours(15, 0, 0, 0);
  
  // If it's already past Saturday 15:00, move to next week
  if (now > nextSaturday) {
    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);
  }
  
  const diff = nextSaturday.getTime() - now.getTime();
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return { days, hours, minutes, seconds };
}

export function getTimeUntilBinaryPayout(): {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
} {
  const now = new Date();
  const nextSaturday = new Date();

  // Set to next Saturday at 15:00 UTC
  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));
  nextSaturday.setUTCHours(15, 0, 0, 0);

  // If it's already past Saturday 15:00, move to next week
  if (now > nextSaturday) {
    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);
  }

  const diff = nextSaturday.getTime() - now.getTime();

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  return { days, hours, minutes, seconds };
}
