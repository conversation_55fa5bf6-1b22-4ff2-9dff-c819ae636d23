/**
 * Deposit Processing Module
 * Extracted from cron route for server-side scheduling
 */

import {
  depositTransactionDb,
  walletBalanceDb,
  transactionDb,
  adminSettingsDb,
  systemLogDb
} from './database';
import { prisma } from './prisma';
import { verifyUSDTTransaction } from './trongrid';

/**
 * Process pending deposits and update confirmations
 * This replaces the /api/cron/process-deposits route functionality
 */
export async function processDeposits() {
  try {
    console.log('Starting deposit processing...');

    // Get admin settings for deposit processing
    const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');
    const currentDepositAddress = await adminSettingsDb.get('depositAddress');

    if (!currentDepositAddress) {
      throw new Error('No deposit address configured');
    }

    // Process pending verification deposits
    const pendingVerificationResult = await processPendingVerificationDeposits(currentDepositAddress);
    
    // Process waiting for confirmations deposits
    const waitingConfirmationsResult = await processWaitingForConfirmationsDeposits(currentDepositAddress, minConfirmations);

    // Process confirmed deposits that need wallet updates
    const confirmedDepositsResult = await processConfirmedDeposits();

    const totalResult = {
      pendingVerificationProcessed: pendingVerificationResult.processed,
      waitingConfirmationsProcessed: waitingConfirmationsResult.processed,
      confirmedDepositsProcessed: confirmedDepositsResult.processed,
      totalProcessed: pendingVerificationResult.processed + waitingConfirmationsResult.processed + confirmedDepositsResult.processed,
      errors: [
        ...pendingVerificationResult.errors,
        ...waitingConfirmationsResult.errors,
        ...confirmedDepositsResult.errors
      ]
    };

    console.log(`Deposit processing completed: ${totalResult.totalProcessed} deposits processed`);
    
    return totalResult;

  } catch (error) {
    console.error('Deposit processing error:', error);
    throw error;
  }
}

/**
 * Process deposits with PENDING_VERIFICATION status
 */
async function processPendingVerificationDeposits(depositAddress: string) {
  const result = { processed: 0, errors: [] as string[] };

  try {
    const pendingDeposits = await depositTransactionDb.getPendingVerificationDeposits();
    console.log(`Found ${pendingDeposits.length} deposits pending verification`);

    for (const deposit of pendingDeposits) {
      try {
        // Verify the transaction
        const verificationResult = await verifyUSDTTransaction(
          deposit.transactionId,
          depositAddress,
          1 // Minimum 1 confirmation for initial verification
        );

        if (verificationResult.isValid) {
          // Update status to waiting for confirmations
          await depositTransactionDb.updateStatus(deposit.transactionId, 'WAITING_FOR_CONFIRMATIONS');
          await depositTransactionDb.updateConfirmations(deposit.transactionId, verificationResult.confirmations);
          
          console.log(`Verified deposit ${deposit.transactionId} with ${verificationResult.confirmations} confirmations`);
          result.processed++;
        } else {
          console.log(`Deposit ${deposit.transactionId} not yet found on blockchain`);
        }
      } catch (error) {
        const errorMsg = `Error verifying deposit ${deposit.transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }
  } catch (error) {
    const errorMsg = `Error processing pending verification deposits: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
  }

  return result;
}

/**
 * Process deposits with WAITING_FOR_CONFIRMATIONS status
 */
async function processWaitingForConfirmationsDeposits(depositAddress: string, minConfirmations: number) {
  const result = { processed: 0, errors: [] as string[] };

  try {
    const waitingDeposits = await depositTransactionDb.getWaitingForConfirmationsDeposits();
    console.log(`Found ${waitingDeposits.length} deposits waiting for confirmations`);

    for (const deposit of waitingDeposits) {
      try {
        // Re-verify to get current confirmation count
        const verificationResult = await verifyUSDTTransaction(
          deposit.transactionId,
          depositAddress,
          1
        );

        if (verificationResult.isValid) {
          // Update confirmation count
          await depositTransactionDb.updateConfirmations(deposit.transactionId, verificationResult.confirmations);

          // Check if it has enough confirmations
          if (verificationResult.confirmations >= minConfirmations) {
            await depositTransactionDb.updateStatus(deposit.transactionId, 'CONFIRMED');
            console.log(`Deposit ${deposit.transactionId} confirmed with ${verificationResult.confirmations} confirmations`);
            result.processed++;
          } else {
            console.log(`Deposit ${deposit.transactionId} has ${verificationResult.confirmations}/${minConfirmations} confirmations`);
          }
        } else {
          console.log(`Deposit ${deposit.transactionId} is no longer valid during confirmation check`);
        }
      } catch (error) {
        const errorMsg = `Error checking confirmations for deposit ${deposit.transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }
  } catch (error) {
    const errorMsg = `Error processing waiting for confirmations deposits: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
  }

  return result;
}

/**
 * Process confirmed deposits that need wallet balance updates
 */
async function processConfirmedDeposits() {
  const result = { processed: 0, errors: [] as string[] };

  try {
    const confirmedDeposits = await depositTransactionDb.getConfirmedDeposits();
    console.log(`Found ${confirmedDeposits.length} confirmed deposits to process`);

    for (const deposit of confirmedDeposits) {
      try {
        // Start transaction
        await prisma.$transaction(async (tx) => {
          // Update wallet balance
          await walletBalanceDb.addDeposit(deposit.userId, deposit.amount);

          // Create transaction record
          await transactionDb.create({
            userId: deposit.userId,
            type: 'DEPOSIT',
            amount: deposit.amount,
            status: 'COMPLETED',
            reference: `deposit:${deposit.transactionId}`,
            description: `USDT Deposit - ${deposit.transactionId}`,
          });

          // Update deposit status to completed
          await depositTransactionDb.updateStatus(deposit.transactionId, 'COMPLETED');
        });

        console.log(`Processed confirmed deposit ${deposit.transactionId} for user ${deposit.userId}: $${deposit.amount}`);
        result.processed++;

        // Log the successful deposit processing
        await systemLogDb.create({
          action: 'DEPOSIT_PROCESSED',
          userId: deposit.userId,
          details: {
            transactionId: deposit.transactionId,
            amount: deposit.amount,
            tronAddress: deposit.tronAddress,
            confirmations: deposit.confirmations,
            processedAt: new Date().toISOString(),
          },
        });

      } catch (error) {
        const errorMsg = `Error processing confirmed deposit ${deposit.transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);

        // Log the error
        await systemLogDb.create({
          action: 'DEPOSIT_PROCESSING_ERROR',
          userId: deposit.userId,
          details: {
            transactionId: deposit.transactionId,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          },
        });
      }
    }
  } catch (error) {
    const errorMsg = `Error processing confirmed deposits: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
  }

  return result;
}

/**
 * Get deposit processing status
 */
export async function getDepositProcessingStatus() {
  try {
    const pendingVerification = await depositTransactionDb.getPendingVerificationDeposits();
    const waitingConfirmations = await depositTransactionDb.getWaitingForConfirmationsDeposits();
    const confirmed = await depositTransactionDb.getConfirmedDeposits();

    return {
      pendingVerification: pendingVerification.length,
      waitingConfirmations: waitingConfirmations.length,
      confirmed: confirmed.length,
      total: pendingVerification.length + waitingConfirmations.length + confirmed.length,
    };
  } catch (error) {
    console.error('Error getting deposit processing status:', error);
    throw error;
  }
}
