'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui';
import { Container, Grid, Flex } from '@/components/layout';
import { SolarPanel, Leaf } from '@/components/icons';
import { Menu, X } from 'lucide-react';

interface PublicLayoutProps {
  children: React.ReactNode;
}

export const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Premium Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-morphism border-b border-white/20">
        <Container>
          <Flex justify="between" align="center" className="h-16 lg:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 lg:space-x-3">
              <div className="relative">
                <SolarPanel className="h-8 w-8 lg:h-10 lg:w-10 text-yellow-500 animate-pulse" />
                <div className="absolute inset-0 bg-yellow-500/20 rounded-full animate-ping"></div>
              </div>
              <span className="text-xl lg:text-3xl font-black text-slate-900">
                HashCoreX
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center gap-8">
              <Link href="/about" className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105">
                About
              </Link>
              <Link href="/how-it-works" className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105">
                How It Works
              </Link>
              <Link href="/contact" className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105">
                Contact
              </Link>
              <Link href="/login">
                <Button variant="ghost" size="md" className="font-semibold">
                  Login
                </Button>
              </Link>
              <Link href="/register">
                <Button variant="primary" size="md" className="font-semibold">
                  Get Started
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6 text-gray-700" />
              ) : (
                <Menu className="h-6 w-6 text-gray-700" />
              )}
            </button>
          </Flex>

          {/* Mobile Navigation Menu */}
          {mobileMenuOpen && (
            <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg">
              <div className="px-4 py-6 space-y-4">
                <Link
                  href="/about"
                  className="block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  About
                </Link>
                <Link
                  href="/how-it-works"
                  className="block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  How It Works
                </Link>
                <Link
                  href="/contact"
                  className="block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Contact
                </Link>
                <div className="pt-4 space-y-3">
                  <Link href="/login" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="ghost" size="md" className="w-full font-semibold">
                      Login
                    </Button>
                  </Link>
                  <Link href="/register" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="primary" size="md" className="w-full font-semibold">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </Container>
      </nav>

      {/* Main Content */}
      <main className="pt-16 lg:pt-20">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-12">
        <Container>
          <Grid cols={{ default: 1, md: 4 }} gap={8}>
            <div>
              <Flex align="center" gap={2} className="mb-4">
                <SolarPanel className="h-8 w-8 text-yellow-400" />
                <span className="text-2xl font-bold">HashCoreX</span>
              </Flex>
              <p className="text-gray-300">
                Sustainable cryptocurrency mining powered by renewable energy.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4 text-white">Platform</h4>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
                <Link href="/how-it-works" className="block text-gray-300 hover:text-white transition-colors">
                  How It Works
                </Link>
                <Link href="/contact" className="block text-gray-300 hover:text-white transition-colors">
                  Contact Us
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4 text-white">Support</h4>
              <div className="space-y-2">
                <Link href="/contact" className="block text-gray-300 hover:text-white transition-colors">
                  Contact Us
                </Link>
                <Link href="/contact" className="block text-gray-300 hover:text-white transition-colors">
                  Help Center
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4 text-white">Legal</h4>
              <div className="space-y-2">
                <Link href="/privacy" className="block text-gray-300 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="block text-gray-300 hover:text-white transition-colors">
                  Terms of Service
                </Link>
              </div>
            </div>
          </Grid>
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
            <p>&copy; 2024 HashCoreX. All rights reserved.</p>
          </div>
        </Container>
      </footer>
    </div>
  );
};
