import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { emailTemplateDb } from '@/lib/database';
import { ErrorLogger } from '@/lib/errorLogger';

// GET - Get specific email template
export async function GET(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const template = await emailTemplateDb.findByName(params.name);
    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: template,
    });

  } catch (error) {
    console.error('Get email template error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'GET_EMAIL_TEMPLATE_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update email template
export async function PUT(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { subject, htmlContent, textContent, isActive } = await request.json();

    // Validation
    if (!subject || !htmlContent) {
      return NextResponse.json(
        { success: false, error: 'Subject and HTML content are required' },
        { status: 400 }
      );
    }

    // Check if template exists
    const existingTemplate = await emailTemplateDb.findByName(params.name);
    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Update template
    const template = await emailTemplateDb.update(params.name, {
      subject,
      htmlContent,
      textContent,
      isActive,
    });

    return NextResponse.json({
      success: true,
      message: 'Email template updated successfully',
      data: template,
    });

  } catch (error) {
    console.error('Update email template error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'UPDATE_EMAIL_TEMPLATE_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete email template
export async function DELETE(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check if template exists
    const existingTemplate = await emailTemplateDb.findByName(params.name);
    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Delete template
    await emailTemplateDb.delete(params.name);

    return NextResponse.json({
      success: true,
      message: 'Email template deleted successfully',
    });

  } catch (error) {
    console.error('Delete email template error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'DELETE_EMAIL_TEMPLATE_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
