import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { miningUnitDb, transactionDb, adminSettingsDb, systemLogDb, walletBalanceDb } from '@/lib/database';
import { getUserMiningUnitsWithEarnings } from '@/lib/miningUnitEarnings';
import { processDirectReferralBonus, addBinaryPoints, getSponsorInfo } from '@/lib/referral';

// GET - Fetch user's mining units with detailed earnings breakdown
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get all mining units with detailed earnings breakdown
    const miningUnits = await getUserMiningUnitsWithEarnings(user.id);

    // Calculate additional information for each unit
    const enrichedMiningUnits = miningUnits.map(unit => {
      const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
      const maxEarnings = unit.investmentAmount * 5;
      const progress = (totalEarnings / maxEarnings) * 100;
      const remainingCapacity = Math.max(0, maxEarnings - totalEarnings);

      return {
        ...unit,
        totalEarningsCalculated: totalEarnings,
        progressPercentage: Math.min(progress, 100),
        remainingCapacity,
        maxEarnings,
        willExpireSoon: progress >= 95, // Flag units close to expiration
      };
    });

    // Sort by creation date for FIFO order
    const sortedUnits = enrichedMiningUnits.sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    return NextResponse.json({
      success: true,
      data: sortedUnits,
      summary: {
        totalUnits: sortedUnits.length,
        activeUnits: sortedUnits.filter(unit => unit.status === 'ACTIVE').length,
        expiredUnits: sortedUnits.filter(unit => unit.status === 'EXPIRED').length,
        totalMiningEarnings: sortedUnits.reduce((sum, unit) => sum + unit.miningEarnings, 0),
        totalReferralEarnings: sortedUnits.reduce((sum, unit) => sum + unit.referralEarnings, 0),
        totalBinaryEarnings: sortedUnits.reduce((sum, unit) => sum + unit.binaryEarnings, 0),
        totalEarnings: sortedUnits.reduce((sum, unit) => sum + (unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings), 0),
        totalInvestment: sortedUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0),
        totalMiningPower: sortedUnits.reduce((sum, unit) => sum + unit.thsAmount, 0),
      },
    });

  } catch (error: any) {
    console.error('Mining units fetch error:', error);

    return NextResponse.json(
      { success: false, error: 'Failed to fetch mining units' },
      { status: 500 }
    );
  }
}

// POST - Purchase new mining unit
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { thsAmount, investmentAmount } = body;

    // Validation
    if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid TH/s amount or investment amount' },
        { status: 400 }
      );
    }

    // Get minimum purchase amount from admin settings
    const minPurchase = parseFloat(await adminSettingsDb.get('MINIMUM_PURCHASE') || '50');
    if (investmentAmount < minPurchase) {
      return NextResponse.json(
        { success: false, error: `Minimum purchase amount is $${minPurchase}` },
        { status: 400 }
      );
    }

    // Get TH/s price from admin settings
    const thsPrice = parseFloat(await adminSettingsDb.get('THS_PRICE') || '50');
    const expectedAmount = thsAmount * thsPrice;
    
    // Allow small rounding differences (within 1%)
    if (Math.abs(investmentAmount - expectedAmount) > expectedAmount * 0.01) {
      return NextResponse.json(
        { success: false, error: 'Investment amount does not match TH/s price' },
        { status: 400 }
      );
    }

    // Calculate dynamic ROI based on unit size
    const { calculateDynamicROI } = await import('@/lib/mining');
    const dynamicROI = await calculateDynamicROI(thsAmount);
    
    // Check wallet balance before purchase
    const walletBalance = await walletBalanceDb.getOrCreate(user.id);

    if (walletBalance.availableBalance < investmentAmount) {
      return NextResponse.json(
        {
          success: false,
          error: `Insufficient balance. Available: $${walletBalance.availableBalance.toFixed(2)}, Required: $${investmentAmount.toFixed(2)}`
        },
        { status: 400 }
      );
    }

    // Use the calculated dynamic ROI
    const dailyROI = dynamicROI;

    // Create mining unit
    const miningUnit = await miningUnitDb.create({
      userId: user.id,
      thsAmount,
      investmentAmount,
      dailyROI,
    });

    // Deduct amount from wallet balance
    await walletBalanceDb.updateBalance(user.id, {
      availableBalance: walletBalance.availableBalance - investmentAmount,
    });

    // Create purchase transaction
    await transactionDb.create({
      userId: user.id,
      type: 'PURCHASE',
      amount: investmentAmount,
      description: `Mining unit purchase - ${thsAmount} TH/s`,
      status: 'COMPLETED',
    });

    // Note: User active status is now computed dynamically for binary tree display

    // Process referral commissions and binary points
    const sponsorInfo = await getSponsorInfo(user.id);
    if (sponsorInfo) {
      try {
        // Process direct referral bonus (10% to sponsor's wallet)
        const bonusAmount = await processDirectReferralBonus(sponsorInfo.id, investmentAmount, user.id);
        console.log(`Direct referral bonus of $${bonusAmount} added to sponsor ${sponsorInfo.id}`);

        // Add binary points to active upliners ($100 = 1 point)
        await addBinaryPoints(user.id, investmentAmount);
        console.log(`Binary points added for investment of $${investmentAmount}`);
      } catch (commissionError) {
        console.error('Error processing commissions:', commissionError);
        // Don't fail the purchase if commission processing fails
      }
    }

    // Log the purchase
    await systemLogDb.create({
      action: 'MINING_UNIT_PURCHASED',
      userId: user.id,
      details: {
        miningUnitId: miningUnit.id,
        thsAmount,
        investmentAmount,
        dailyROI,
        sponsorId: sponsorInfo?.id,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Send email notification
    try {
      const { emailNotificationService } = await import('@/lib/emailNotificationService');
      await emailNotificationService.sendMiningUnitPurchaseNotification({
        userId: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        thsAmount,
        investmentAmount,
        dailyROI,
        purchaseDate: miningUnit.createdAt,
        expiryDate: miningUnit.expiryDate,
      });
    } catch (emailError) {
      console.error('Failed to send mining unit purchase email:', emailError);
      // Don't fail the purchase if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Mining unit purchased successfully',
      data: miningUnit,
    });

  } catch (error: any) {
    console.error('Mining unit purchase error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to purchase mining unit' },
      { status: 500 }
    );
  }
}
