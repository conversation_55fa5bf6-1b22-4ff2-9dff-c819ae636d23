import { prisma } from '@/lib/prisma';
import { transactionDb, systemLogDb } from '@/lib/database';

export interface MiningUnitWithEarnings {
  id: string;
  userId: string;
  thsAmount: number;
  investmentAmount: number;
  startDate: Date;
  expiryDate: Date;
  dailyROI: number;
  totalEarned: number;
  miningEarnings: number;
  referralEarnings: number;
  binaryEarnings: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EarningsAllocation {
  miningUnitId: string;
  amount: number;
  remainingCapacity: number;
}

/**
 * Get active mining units for a user ordered by creation date (FIFO)
 */
export async function getActiveMiningUnitsFIFO(userId: string): Promise<MiningUnitWithEarnings[]> {
  return await prisma.miningUnit.findMany({
    where: {
      userId,
      status: 'ACTIVE',
      expiryDate: {
        gt: new Date(),
      },
    },
    orderBy: {
      createdAt: 'asc', // FIFO order - oldest first
    },
  });
}

/**
 * Calculate remaining earning capacity for a mining unit (5x - current earnings)
 */
export function calculateRemainingCapacity(unit: MiningUnitWithEarnings): number {
  const maxEarnings = unit.investmentAmount * 5;
  const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
  return Math.max(0, maxEarnings - currentTotalEarnings);
}

/**
 * Check if a mining unit should expire based on 5x earnings
 */
export function shouldExpireUnit(unit: MiningUnitWithEarnings): boolean {
  const maxEarnings = unit.investmentAmount * 5;
  const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
  return currentTotalEarnings >= maxEarnings;
}

/**
 * Allocate earnings to mining units using FIFO logic
 * Returns array of allocations showing how much was allocated to each unit
 */
export async function allocateEarningsToUnits(
  userId: string,
  totalAmount: number,
  earningType: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS',
  transactionId: string,
  description: string
): Promise<EarningsAllocation[]> {
  const activeMiningUnits = await getActiveMiningUnitsFIFO(userId);
  
  if (activeMiningUnits.length === 0) {
    throw new Error('No active mining units found for earnings allocation');
  }

  const allocations: EarningsAllocation[] = [];
  let remainingAmount = totalAmount;

  for (const unit of activeMiningUnits) {
    if (remainingAmount <= 0) break;

    const remainingCapacity = calculateRemainingCapacity(unit);
    
    if (remainingCapacity <= 0) {
      // Unit is already at 5x capacity, skip it
      continue;
    }

    // Allocate the minimum of remaining amount or remaining capacity
    const allocationAmount = Math.min(remainingAmount, remainingCapacity);
    
    if (allocationAmount > 0) {
      // Update the mining unit earnings based on type
      const updateData: any = {};
      switch (earningType) {
        case 'MINING_EARNINGS':
          updateData.miningEarnings = { increment: allocationAmount };
          break;
        case 'DIRECT_REFERRAL':
          updateData.referralEarnings = { increment: allocationAmount };
          break;
        case 'BINARY_BONUS':
          updateData.binaryEarnings = { increment: allocationAmount };
          break;
      }

      // Update total earned for legacy compatibility
      updateData.totalEarned = { increment: allocationAmount };

      await prisma.miningUnit.update({
        where: { id: unit.id },
        data: updateData,
      });

      // Create earnings allocation record
      await prisma.miningUnitEarningsAllocation.create({
        data: {
          miningUnitId: unit.id,
          transactionId,
          earningType,
          amount: allocationAmount,
          description,
        },
      });

      allocations.push({
        miningUnitId: unit.id,
        amount: allocationAmount,
        remainingCapacity: remainingCapacity - allocationAmount,
      });

      remainingAmount -= allocationAmount;

      // Check if unit should expire after this allocation
      const updatedUnit = await prisma.miningUnit.findUnique({
        where: { id: unit.id },
      });

      if (updatedUnit && shouldExpireUnit(updatedUnit as MiningUnitWithEarnings)) {
        await expireMiningUnit(unit.id, '5x_investment_reached');
      }
    }
  }

  // If there's still remaining amount, it means all units are at capacity
  if (remainingAmount > 0) {
    console.warn(`Unable to allocate ${remainingAmount} to mining units - all units at capacity`);
    
    // Log this situation
    await systemLogDb.create({
      action: 'EARNINGS_ALLOCATION_OVERFLOW',
      userId,
      details: {
        totalAmount,
        allocatedAmount: totalAmount - remainingAmount,
        overflowAmount: remainingAmount,
        earningType,
        reason: 'all_units_at_capacity',
      },
    });
  }

  return allocations;
}

/**
 * Expire a mining unit and log the action
 */
export async function expireMiningUnit(unitId: string, reason: string): Promise<void> {
  const unit = await prisma.miningUnit.findUnique({
    where: { id: unitId },
    include: {
      user: {
        select: {
          email: true,
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  if (!unit) {
    throw new Error(`Mining unit ${unitId} not found`);
  }

  await prisma.miningUnit.update({
    where: { id: unitId },
    data: { status: 'EXPIRED' },
  });

  const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;

  await systemLogDb.create({
    action: 'MINING_UNIT_EXPIRED',
    userId: unit.userId,
    details: {
      miningUnitId: unitId,
      reason,
      totalEarned: totalEarnings,
      miningEarnings: unit.miningEarnings,
      referralEarnings: unit.referralEarnings,
      binaryEarnings: unit.binaryEarnings,
      investmentAmount: unit.investmentAmount,
      multiplier: totalEarnings / unit.investmentAmount,
    },
  });

  // Send email notification
  try {
    const { emailNotificationService } = await import('@/lib/emailNotificationService');
    await emailNotificationService.sendMiningUnitExpiryNotification({
      userId: unit.userId,
      email: unit.user.email,
      firstName: unit.user.firstName,
      lastName: unit.user.lastName,
      thsAmount: unit.thsAmount,
      investmentAmount: unit.investmentAmount,
      totalEarned: totalEarnings,
      purchaseDate: unit.createdAt.toISOString(),
      expiryDate: unit.expiryDate.toISOString(),
      expiryReason: reason === '24_months_reached' ? 'TIME_LIMIT' : 'RETURN_LIMIT',
    });
  } catch (emailError) {
    console.error('Failed to send mining unit expiry email:', emailError);
    // Don't fail the expiry if email fails
  }

  console.log(`Mining unit ${unitId} expired due to ${reason}. Total earnings: ${totalEarnings}`);
}

/**
 * Get detailed earnings breakdown for a user's mining units
 */
export async function getUserMiningUnitsWithEarnings(userId: string): Promise<MiningUnitWithEarnings[]> {
  return await prisma.miningUnit.findMany({
    where: { userId },
    orderBy: { createdAt: 'asc' },
  });
}

/**
 * Get earnings allocation history for a mining unit
 */
export async function getMiningUnitEarningsHistory(miningUnitId: string) {
  return await prisma.miningUnitEarningsAllocation.findMany({
    where: { miningUnitId },
    include: {
      transaction: true,
    },
    orderBy: { allocatedAt: 'desc' },
  });
}
