// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  firebaseUid?: string;
  referralId: string;
  leftReferralId?: string;
  rightReferralId?: string;
  role: UserRole;
  kycStatus: KYCStatus;
  profilePicture?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

export interface UserProfile extends User {
  totalTHS: number;
  walletBalance: number;
  totalEarnings: number;
  activeUnits: number;
}

// KYC Types
export enum KYCStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export interface KYCDocument {
  id: string;
  userId: string;
  documentType: DocumentType;
  idType?: IDType;
  documentSide?: DocumentSide;
  filePath: string;
  status: KYCStatus;
  reviewedAt?: Date;
  createdAt: Date;
}

export enum DocumentType {
  ID_DOCUMENT = 'ID_DOCUMENT',
  SELFIE = 'SELFIE'
}

export enum IDType {
  NATIONAL_ID = 'NATIONAL_ID',
  PASSPORT = 'PASSPORT',
  DRIVING_LICENSE = 'DRIVING_LICENSE'
}

export enum DocumentSide {
  FRONT = 'FRONT',
  BACK = 'BACK'
}

// Mining Unit Types
export interface MiningUnit {
  id: string;
  userId: string;
  thsAmount: number;
  investmentAmount: number;
  startDate: Date;
  expiryDate: Date;
  dailyROI: number;
  totalEarned: number;
  status: MiningUnitStatus;
  createdAt: Date;
}

export enum MiningUnitStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED'
}

// Transaction Types
export interface Transaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  description: string;
  status: TransactionStatus;
  createdAt: Date;
}

export enum TransactionType {
  MINING_EARNINGS = 'MINING_EARNINGS',
  DIRECT_REFERRAL = 'DIRECT_REFERRAL',
  BINARY_BONUS = 'BINARY_BONUS',
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
  PURCHASE = 'PURCHASE'
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Referral Types
export interface Referral {
  id: string;
  referrerId: string;
  referredId: string;
  placementSide: 'LEFT' | 'RIGHT';
  commissionEarned: number;
  createdAt: Date;
}

export interface BinaryPoints {
  id: string;
  userId: string;
  leftPoints: number;
  rightPoints: number;
  matchedPoints: number;
  flushDate?: Date;
  createdAt: Date;
}

// Withdrawal Types
export interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  usdtAddress: string;
  status: WithdrawalStatus;
  processedBy?: string;
  processedAt?: Date;
  rejectionReason?: string;
  txid?: string;
  createdAt: Date;
}

export enum WithdrawalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED'
}

export enum DepositStatus {
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
  PENDING = 'PENDING',
  VERIFYING = 'VERIFYING',
  WAITING_FOR_CONFIRMATIONS = 'WAITING_FOR_CONFIRMATIONS',
  CONFIRMED = 'CONFIRMED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REJECTED = 'REJECTED'
}

// Wallet Types
export interface WalletBalance {
  id: string;
  userId: string;
  availableBalance: number;
  pendingBalance: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalEarnings: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DepositTransaction {
  id: string;
  userId: string;
  transactionId: string;
  amount: number;
  usdtAmount: number;
  tronAddress: string;
  senderAddress?: string;
  status: DepositStatus;
  blockNumber?: string;
  blockTimestamp?: Date;
  confirmations: number;
  verifiedAt?: Date;
  processedAt?: Date;
  failureReason?: string;
  createdAt: Date;
  updatedAt: Date;
  user?: User;
}

// Admin Types
export interface AdminSettings {
  key: string;
  value: string;
  updatedAt: Date;
}

export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalTHSSold: number;
  totalEarnings: number;
  pendingKYC: number;
  pendingWithdrawals: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  referralCode?: string;
}

export interface PurchaseForm {
  thsAmount: number;
  investmentAmount: number;
}

export interface WithdrawalForm {
  amount: number;
  usdtAddress: string;
}

export interface DepositForm {
  transactionId: string;
  amount?: number; // Optional, will be verified from blockchain
}

// Dashboard Types
export interface DashboardStats {
  totalTHS: number;
  estimatedEarnings: {
    next7Days: number;
    next30Days: number;
    next365Days: number;
  };
  walletBalance: number;
  activeUnits: number;
  totalEarnings: number;
}

export interface BinaryTreeNode {
  userId: string;
  email: string;
  leftChild?: BinaryTreeNode;
  rightChild?: BinaryTreeNode;
  totalPoints: number;
  isActive: boolean;
}

// Component Props Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
