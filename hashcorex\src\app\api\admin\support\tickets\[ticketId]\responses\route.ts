import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { supportTicketDb, ticketResponseDb } from '@/lib/database';

// POST - Add admin response to support ticket
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { ticketId } = await params;
    const body = await request.json();
    const { message } = body;

    // Validate required fields
    if (!message || !message.trim()) {
      return NextResponse.json(
        { success: false, error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if ticket exists
    const ticket = await supportTicketDb.findById(ticketId);
    if (!ticket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Check if ticket is closed
    if (ticket.status === 'CLOSED') {
      return NextResponse.json(
        { success: false, error: 'Cannot add response to closed ticket' },
        { status: 400 }
      );
    }

    // Create the admin response
    const response = await ticketResponseDb.create({
      ticketId,
      userId: user.id, // Admin user ID
      message: message.trim(),
      isAdmin: true,
    });

    // Update ticket status to IN_PROGRESS if it was OPEN
    if (ticket.status === 'OPEN') {
      await supportTicketDb.updateStatus(ticketId, 'IN_PROGRESS');
    }

    return NextResponse.json({
      success: true,
      data: response,
      message: 'Admin response added successfully',
    });

  } catch (error: any) {
    console.error('Admin ticket response creation error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to add admin response' },
      { status: 500 }
    );
  }
}
