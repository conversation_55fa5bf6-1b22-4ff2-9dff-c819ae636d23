'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import {
  CreditCard,
  Search,
  Filter,
  Check,
  X,
  Clock,
  DollarSign,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Copy
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

interface WithdrawalRequest {
  id: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  amount: number;
  walletAddress: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  requestedAt: string;
  processedAt?: string;
  rejectionReason?: string;
  transactionHash?: string;
}

export const WithdrawalManagement: React.FC = () => {
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'completed'>('all');
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | 'complete' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [transactionHash, setTransactionHash] = useState('');
  const [processing, setProcessing] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  useEffect(() => {
    fetchWithdrawals();
  }, [searchTerm, filterStatus]);

  const fetchWithdrawals = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: searchTerm,
        status: filterStatus,
      });

      const response = await fetch(`/api/admin/withdrawals?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWithdrawals(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch withdrawals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const handleWithdrawalAction = async (
    withdrawalId: string, 
    action: 'approve' | 'reject' | 'complete', 
    data?: { rejectionReason?: string; transactionHash?: string }
  ) => {
    try {
      setProcessing(true);
      const response = await fetch('/api/admin/withdrawals/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          withdrawalId,
          action: action.toUpperCase(),
          ...data,
        }),
      });

      if (response.ok) {
        fetchWithdrawals(); // Refresh the list
        setSelectedWithdrawal(null);
        setReviewAction(null);
        setRejectionReason('');
        setTransactionHash('');
      }
    } catch (error) {
      console.error('Failed to process withdrawal action:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const configs = {
      PENDING: { color: 'bg-yellow-900 text-yellow-300 border border-yellow-700', icon: Clock },
      APPROVED: { color: 'bg-blue-900 text-blue-300 border border-blue-700', icon: CheckCircle },
      REJECTED: { color: 'bg-red-900 text-red-300 border border-red-700', icon: XCircle },
      COMPLETED: { color: 'bg-green-900 text-green-300 border border-green-700', icon: CheckCircle },
    };

    const config = configs[status as keyof typeof configs];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {status}
      </span>
    );
  };

  const getTotalPendingAmount = () => {
    return withdrawals
      .filter(w => w.status === 'PENDING')
      .reduce((sum, w) => sum + w.amount, 0);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="flex justify-end">
        <div className="text-right">
          <div className="text-sm text-slate-400">Pending Amount</div>
          <div className="text-2xl font-bold text-yellow-400">
            {formatCurrency(getTotalPendingAmount())}
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search by user email or wallet address..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="all">All Withdrawals</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="completed">Completed</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Withdrawals List */}
      <div className="grid gap-4">
        {withdrawals.length === 0 ? (
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-12 text-center">
              <CreditCard className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Withdrawal Requests</h3>
              <p className="text-slate-400">No withdrawal requests match your current filters.</p>
            </CardContent>
          </Card>
        ) : (
          withdrawals.map((withdrawal) => (
            <Card key={withdrawal.id} className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-slate-400" />
                        <span className="font-medium text-white">
                          {withdrawal.user.firstName} {withdrawal.user.lastName}
                        </span>
                      </div>
                      {getStatusBadge(withdrawal.status)}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4">
                      <div>
                        <span className="font-medium text-slate-300">Email:</span> {withdrawal.user.email}
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">User ID:</span> {withdrawal.user.referralId}
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        <span className="font-medium text-slate-300">Amount:</span> {formatCurrency(withdrawal.amount)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium text-slate-300">Requested:</span> {formatDateTime(withdrawal.requestedAt)}
                      </div>
                    </div>

                    <div className="text-sm text-slate-400 mb-4">
                      <span className="font-medium text-slate-300">Wallet Address:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded break-all text-slate-300 flex-1">
                          {withdrawal.walletAddress}
                        </div>
                        <button
                          onClick={() => handleCopyToClipboard(withdrawal.walletAddress, `wallet-${withdrawal.id}`)}
                          className="p-2 text-slate-400 hover:text-white transition-colors flex-shrink-0 bg-slate-700 border border-slate-600 rounded"
                          title="Copy wallet address"
                        >
                          {copiedField === `wallet-${withdrawal.id}` ? (
                            <Check className="w-4 h-4 text-green-400" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>

                    {withdrawal.transactionHash && (
                      <div className="text-sm text-slate-400 mb-4">
                        <span className="font-medium text-slate-300">Transaction Hash:</span>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded break-all text-green-300 flex-1">
                            {withdrawal.transactionHash}
                          </div>
                          <button
                            onClick={() => handleCopyToClipboard(withdrawal.transactionHash || '', `hash-${withdrawal.id}`)}
                            className="p-2 text-slate-400 hover:text-white transition-colors flex-shrink-0 bg-green-900/20 border border-green-700 rounded"
                            title="Copy transaction hash"
                          >
                            {copiedField === `hash-${withdrawal.id}` ? (
                              <Check className="w-4 h-4 text-green-400" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    )}

                    {withdrawal.rejectionReason && (
                      <div className="bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4">
                        <div className="flex items-center gap-2 text-red-300 text-sm font-medium mb-1">
                          <X className="h-4 w-4" />
                          Rejection Reason
                        </div>
                        <p className="text-red-400 text-sm">{withdrawal.rejectionReason}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {withdrawal.status === 'PENDING' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedWithdrawal(withdrawal);
                            setReviewAction('approve');
                          }}
                          className="border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20"
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Approve
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedWithdrawal(withdrawal);
                            setReviewAction('reject');
                          }}
                          className="border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}

                    {withdrawal.status === 'APPROVED' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedWithdrawal(withdrawal);
                          setReviewAction('complete');
                        }}
                        className="border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20"
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Mark Complete
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Action Modal */}
      {selectedWithdrawal && reviewAction && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {reviewAction === 'approve' && 'Approve Withdrawal'}
              {reviewAction === 'reject' && 'Reject Withdrawal'}
              {reviewAction === 'complete' && 'Complete Withdrawal'}
            </h3>

            <div className="mb-4">
              <p className="text-slate-400 mb-2">
                User: <span className="font-medium text-white">{selectedWithdrawal.user.firstName} {selectedWithdrawal.user.lastName}</span>
              </p>
              <p className="text-slate-400">
                Amount: <span className="font-medium text-white">{formatCurrency(selectedWithdrawal.amount)}</span>
              </p>
            </div>

            {reviewAction === 'reject' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  rows={3}
                  placeholder="Please provide a reason for rejection..."
                  required
                />
              </div>
            )}

            {reviewAction === 'complete' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Transaction Hash *
                </label>
                <Input
                  value={transactionHash}
                  onChange={(e) => setTransactionHash(e.target.value)}
                  placeholder="Enter blockchain transaction hash..."
                  className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                  required
                />
              </div>
            )}

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedWithdrawal(null);
                  setReviewAction(null);
                  setRejectionReason('');
                  setTransactionHash('');
                }}
                disabled={processing}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  const data: any = {};
                  if (reviewAction === 'reject') data.rejectionReason = rejectionReason;
                  if (reviewAction === 'complete') data.transactionHash = transactionHash;

                  handleWithdrawalAction(selectedWithdrawal.id, reviewAction, data);
                }}
                disabled={
                  processing ||
                  (reviewAction === 'reject' && !rejectionReason.trim()) ||
                  (reviewAction === 'complete' && !transactionHash.trim())
                }
                loading={processing}
                className={
                  reviewAction === 'reject'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : reviewAction === 'approve'
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }
              >
                {reviewAction === 'approve' && 'Approve'}
                {reviewAction === 'reject' && 'Reject'}
                {reviewAction === 'complete' && 'Mark Complete'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
