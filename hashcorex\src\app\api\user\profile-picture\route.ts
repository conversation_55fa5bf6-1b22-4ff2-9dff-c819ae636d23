import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { userDb } from '@/lib/database';
import { writeFile, mkdir, unlink } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { existsSync } from 'fs';

// POST - Upload profile picture
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('profilePicture') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, error: 'File must be an image' },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size must be less than 5MB' },
        { status: 400 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'profile-pictures');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const fileName = `${user.id}_${uuidv4()}.${fileExtension}`;
    const filePath = join(uploadDir, fileName);
    const publicPath = `/uploads/profile-pictures/${fileName}`;

    // Remove old profile picture if exists
    const currentUser = await userDb.findById(user.id);
    if (currentUser?.profilePicture) {
      const oldFilePath = join(process.cwd(), 'public', currentUser.profilePicture);
      try {
        if (existsSync(oldFilePath)) {
          await unlink(oldFilePath);
        }
      } catch (error) {
        console.warn('Failed to delete old profile picture:', error);
      }
    }

    // Save new file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Update user profile picture in database
    const updatedUser = await userDb.updateProfilePicture(user.id, publicPath);

    return NextResponse.json({
      success: true,
      message: 'Profile picture updated successfully',
      data: {
        profilePicture: publicPath,
        user: updatedUser,
      },
    });

  } catch (error) {
    console.error('Profile picture upload error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload profile picture' },
      { status: 500 }
    );
  }
}

// DELETE - Remove profile picture
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current user data
    const currentUser = await userDb.findById(user.id);
    if (!currentUser?.profilePicture) {
      return NextResponse.json(
        { success: false, error: 'No profile picture to remove' },
        { status: 400 }
      );
    }

    // Remove file from filesystem
    const filePath = join(process.cwd(), 'public', currentUser.profilePicture);
    try {
      if (existsSync(filePath)) {
        await unlink(filePath);
      }
    } catch (error) {
      console.warn('Failed to delete profile picture file:', error);
    }

    // Update user profile picture in database
    const updatedUser = await userDb.updateProfilePicture(user.id, null);

    return NextResponse.json({
      success: true,
      message: 'Profile picture removed successfully',
      data: {
        user: updatedUser,
      },
    });

  } catch (error) {
    console.error('Profile picture removal error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to remove profile picture' },
      { status: 500 }
    );
  }
}
