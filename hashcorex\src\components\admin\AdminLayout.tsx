'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import { Container, Flex } from '@/components/layout';
import { Button, ProfileImage } from '@/components/ui';
import { SolarPanel } from '@/components/icons';
import {
  LayoutDashboard,
  Users,
  Shield,
  CreditCard,
  Settings,
  FileText,
  LogOut,
  Menu,
  X,
  Bell,
  ChevronDown,
  User,
  ArrowLeft,
  DollarSign,
  MessageCircle,
  ArrowUpDown,
  TrendingUp,
  Mail,
  Zap,
  BarChart3
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  activeTab,
  onTabChange,
}) => {
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Admin navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'users', label: 'User Management', icon: Users },
    { id: 'kyc', label: 'KYC Review', icon: Shield },
    { id: 'deposits', label: 'Deposits', icon: DollarSign },
    { id: 'withdrawals', label: 'Withdrawals', icon: CreditCard },
    { id: 'mining-management', label: 'Mining Management', icon: Zap },
    { id: 'account-audit', label: 'Account Audit', icon: BarChart3 },
    { id: 'support', label: 'Support Tickets', icon: MessageCircle },
    { id: 'binary-points', label: 'Binary Points', icon: ArrowUpDown },
    { id: 'referral-commissions', label: 'Referral Commissions', icon: TrendingUp },
    { id: 'email-settings', label: 'Email Settings', icon: Mail },
    { id: 'settings', label: 'System Settings', icon: Settings },
    { id: 'logs', label: 'System Logs', icon: FileText },
  ];

  const handleLogout = async () => {
    await logout();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-slate-900 flex admin-panel" data-admin-panel="true">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Fixed Sidebar */}
      <aside className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700
        transform transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-screen">
          {/* Logo Header */}
          <div className="flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <SolarPanel className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold text-white">HashCoreX</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1.5 rounded-lg text-slate-400"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Admin Badge */}
          <div className="px-3 py-3 bg-red-600 border-b border-slate-700">
            <div className="flex items-center space-x-2 text-white">
              <Shield className="h-4 w-4" />
              <span className="text-sm font-semibold">Admin Panel</span>
            </div>
          </div>

          {/* Navigation Menu */}
          <nav className="flex-1 px-3 py-4 space-y-1 min-h-0">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onTabChange(item.id);
                    setSidebarOpen(false);
                  }}
                  className={`
                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group
                    ${isActive
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-slate-300'
                    }
                  `}
                >
                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-slate-400'}`} />
                  <span className="font-medium text-sm">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Back to Dashboard */}
          <div className="px-3 py-3 border-t border-slate-700">
            <Link
              href="/dashboard"
              className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="font-medium text-sm">Back to Dashboard</span>
            </Link>
          </div>

          {/* Sidebar Footer */}
          <div className="px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0">
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group"
            >
              <LogOut className="h-4 w-4" />
              <span className="font-medium text-sm">Logout</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0 lg:ml-64">
        {/* Top Navigation Bar */}
        <header className="bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30">
          <div className="px-4 sm:px-6 lg:px-8 xl:px-12">
            <Flex justify="between" align="center" className="h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 rounded-lg text-slate-400"
                >
                  <Menu className="h-6 w-6" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-white capitalize tracking-wide drop-shadow-lg">
                    {navigationItems.find(item => item.id === activeTab)?.label || 'Admin Dashboard'}
                  </h1>
                  <p className="text-sm text-slate-300 hidden sm:block font-medium">
                    Manage platform operations and user activities
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                {/* Notifications */}
                <button className="relative p-2 rounded-lg text-slate-400">
                  <Bell className="h-5 w-5" />
                  <span className="absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full"></span>
                </button>

                {/* Admin Badge */}
                <div className="px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500">
                  ADMIN
                </div>

                {/* User Dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                    className="flex items-center space-x-2 p-1 rounded-lg"
                  >
                    <ProfileImage
                      src={user?.profilePicture}
                      alt="Profile"
                      size={32}
                      className="rounded-lg"
                      fallbackText={user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}
                      fallbackBgColor="bg-orange-600"
                      loading="lazy"
                    />
                    <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Dropdown Menu */}
                  {userDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50">
                      {/* User Info */}
                      <div className="px-4 py-3 border-b border-slate-700">
                        <div className="flex items-center space-x-3">
                          <ProfileImage
                            src={user?.profilePicture}
                            alt="Profile"
                            size={40}
                            className="rounded-lg"
                            fallbackText={user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}
                            fallbackBgColor="bg-orange-600"
                            loading="lazy"
                          />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-semibold text-white truncate">
                              {user?.firstName && user?.lastName
                                ? `${user.firstName} ${user.lastName}`
                                : user?.email.split('@')[0]
                              }
                            </p>
                            <p className="text-xs text-slate-400">
                              ID: {user?.referralId}
                            </p>
                            <p className="text-xs text-red-400 font-medium">
                              Administrator
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="py-1">
                        <Link
                          href="/dashboard"
                          onClick={() => setUserDropdownOpen(false)}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300"
                        >
                          <User className="h-4 w-4" />
                          <span>User Dashboard</span>
                        </Link>
                        <div className="border-t border-slate-700 my-1"></div>
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false);
                            handleLogout();
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400"
                        >
                          <LogOut className="h-4 w-4" />
                          <span>Sign Out</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Flex>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 bg-slate-900 overflow-y-auto relative">
          {/* Fixed Background Image */}
          <div
            className="fixed inset-0 bg-cover bg-center bg-no-repeat opacity-10 pointer-events-none"
            style={{
              backgroundImage: 'url(/admin_background.jpg)',
              backgroundAttachment: 'fixed',
              zIndex: 0,
            }}
          />
          {/* Content Overlay */}
          <div className="relative z-10 px-4 sm:px-6 lg:px-8 xl:px-12 py-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
