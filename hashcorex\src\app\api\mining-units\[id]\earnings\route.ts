import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { getMiningUnitEarningsHistory } from '@/lib/miningUnitEarnings';
import { prisma } from '@/lib/prisma';

// GET - Fetch detailed earnings history for a specific mining unit
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { id: miningUnitId } = await params;

    // Verify the mining unit belongs to the authenticated user
    const miningUnit = await prisma.miningUnit.findUnique({
      where: { id: miningUnitId },
      select: { userId: true, investmentAmount: true, thsAmount: true, status: true },
    });

    if (!miningUnit) {
      return NextResponse.json(
        { success: false, error: 'Mining unit not found' },
        { status: 404 }
      );
    }

    if (miningUnit.userId !== user.id) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get detailed earnings history
    const earningsHistory = await getMiningUnitEarningsHistory(miningUnitId);

    // Group earnings by type for summary
    const earningsSummary = earningsHistory.reduce((acc, allocation) => {
      const type = allocation.earningType;
      if (!acc[type]) {
        acc[type] = {
          totalAmount: 0,
          count: 0,
          allocations: [],
        };
      }
      acc[type].totalAmount += allocation.amount;
      acc[type].count += 1;
      acc[type].allocations.push({
        amount: allocation.amount,
        date: allocation.allocatedAt,
        description: allocation.description,
        transactionId: allocation.transactionId,
      });
      return acc;
    }, {} as Record<string, any>);

    // Calculate totals
    const totalAllocated = earningsHistory.reduce((sum, allocation) => sum + allocation.amount, 0);
    const maxEarnings = miningUnit.investmentAmount * 5;
    const remainingCapacity = Math.max(0, maxEarnings - totalAllocated);
    const progressPercentage = (totalAllocated / maxEarnings) * 100;

    return NextResponse.json({
      success: true,
      data: {
        miningUnit: {
          id: miningUnitId,
          thsAmount: miningUnit.thsAmount,
          investmentAmount: miningUnit.investmentAmount,
          status: miningUnit.status,
          maxEarnings,
        },
        earnings: {
          totalAllocated,
          remainingCapacity,
          progressPercentage: Math.min(progressPercentage, 100),
          summary: earningsSummary,
        },
        history: earningsHistory.map(allocation => ({
          id: allocation.id,
          earningType: allocation.earningType,
          amount: allocation.amount,
          description: allocation.description,
          allocatedAt: allocation.allocatedAt,
          transaction: {
            id: allocation.transaction.id,
            type: allocation.transaction.type,
            status: allocation.transaction.status,
            createdAt: allocation.transaction.createdAt,
          },
        })),
      },
    });

  } catch (error: any) {
    console.error('Mining unit earnings history fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch earnings history' },
      { status: 500 }
    );
  }
}
