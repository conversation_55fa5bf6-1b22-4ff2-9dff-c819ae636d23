import { NextRequest, NextResponse } from 'next/server';

// Cache for crypto prices (in-memory cache for simplicity)
let priceCache: {
  data: any;
  timestamp: number;
} | null = null;

const CACHE_DURATION = 60000; // 1 minute in milliseconds

// Top cryptocurrencies to fetch
const CRYPTO_IDS = [
  'bitcoin', 'ethereum', 'tether', 'binancecoin', 'solana', 'usd-coin', 'xrp', 
  'staked-ether', 'dogecoin', 'cardano', 'tron', 'avalanche-2', 'chainlink', 
  'polygon', 'wrapped-bitcoin', 'internet-computer', 'near', 'uniswap', 
  'litecoin', 'dai', 'ethereum-classic', 'stellar', 'monero', 'bitcoin-cash', 'cosmos'
];

// GET - Fetch crypto prices
export async function GET(request: NextRequest) {
  try {
    // Check if we have cached data that's still fresh
    if (priceCache && (Date.now() - priceCache.timestamp) < CACHE_DURATION) {
      return NextResponse.json({
        success: true,
        data: priceCache.data,
        cached: true,
        timestamp: priceCache.timestamp,
      });
    }

    // Fetch fresh data from CoinGecko API
    const response = await fetch(
      `https://api.coingecko.com/api/v3/simple/price?ids=${CRYPTO_IDS.join(',')}&vs_currencies=usd&include_24hr_change=true`,
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'HashCoreX/1.0',
        },
        // Add timeout
        signal: AbortSignal.timeout(10000), // 10 seconds timeout
      }
    );

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();

    // Transform the data to a more usable format
    const transformedData = CRYPTO_IDS.map(id => {
      const priceData = data[id];
      if (!priceData) return null;

      return {
        id,
        symbol: getCryptoSymbol(id),
        name: getCryptoName(id),
        current_price: priceData.usd,
        price_change_percentage_24h: priceData.usd_24h_change || 0,
        image: getCryptoImage(id),
      };
    }).filter(Boolean);

    // Update cache
    priceCache = {
      data: transformedData,
      timestamp: Date.now(),
    };

    return NextResponse.json({
      success: true,
      data: transformedData,
      cached: false,
      timestamp: Date.now(),
    });

  } catch (error) {
    console.error('Error fetching crypto prices:', error);

    // Return cached data if available, even if stale
    if (priceCache) {
      return NextResponse.json({
        success: true,
        data: priceCache.data,
        cached: true,
        stale: true,
        timestamp: priceCache.timestamp,
        error: 'Using cached data due to API error',
      });
    }

    // Return fallback static data if no cache available
    const fallbackData = getFallbackCryptoData();
    
    return NextResponse.json({
      success: true,
      data: fallbackData,
      fallback: true,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now(),
    });
  }
}

// Helper functions
function getCryptoSymbol(id: string): string {
  const symbolMap: Record<string, string> = {
    'bitcoin': 'BTC',
    'ethereum': 'ETH',
    'tether': 'USDT',
    'binancecoin': 'BNB',
    'solana': 'SOL',
    'usd-coin': 'USDC',
    'xrp': 'XRP',
    'staked-ether': 'stETH',
    'dogecoin': 'DOGE',
    'cardano': 'ADA',
    'tron': 'TRX',
    'avalanche-2': 'AVAX',
    'chainlink': 'LINK',
    'polygon': 'MATIC',
    'wrapped-bitcoin': 'WBTC',
    'internet-computer': 'ICP',
    'near': 'NEAR',
    'uniswap': 'UNI',
    'litecoin': 'LTC',
    'dai': 'DAI',
    'ethereum-classic': 'ETC',
    'stellar': 'XLM',
    'monero': 'XMR',
    'bitcoin-cash': 'BCH',
    'cosmos': 'ATOM',
  };
  return symbolMap[id] || id.toUpperCase();
}

function getCryptoName(id: string): string {
  const nameMap: Record<string, string> = {
    'bitcoin': 'Bitcoin',
    'ethereum': 'Ethereum',
    'tether': 'Tether',
    'binancecoin': 'BNB',
    'solana': 'Solana',
    'usd-coin': 'USD Coin',
    'xrp': 'XRP',
    'staked-ether': 'Lido Staked Ether',
    'dogecoin': 'Dogecoin',
    'cardano': 'Cardano',
    'tron': 'TRON',
    'avalanche-2': 'Avalanche',
    'chainlink': 'Chainlink',
    'polygon': 'Polygon',
    'wrapped-bitcoin': 'Wrapped Bitcoin',
    'internet-computer': 'Internet Computer',
    'near': 'NEAR Protocol',
    'uniswap': 'Uniswap',
    'litecoin': 'Litecoin',
    'dai': 'Dai',
    'ethereum-classic': 'Ethereum Classic',
    'stellar': 'Stellar',
    'monero': 'Monero',
    'bitcoin-cash': 'Bitcoin Cash',
    'cosmos': 'Cosmos',
  };
  return nameMap[id] || id;
}

function getCryptoImage(id: string): string {
  return `/crypto-icons/${id}.png`;
}

// Fallback data in case all APIs fail
function getFallbackCryptoData() {
  return [
    { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin', current_price: 45000, price_change_percentage_24h: 2.5, image: '/crypto-icons/bitcoin.png' },
    { id: 'ethereum', symbol: 'ETH', name: 'Ethereum', current_price: 2800, price_change_percentage_24h: 1.8, image: '/crypto-icons/ethereum.png' },
    { id: 'tether', symbol: 'USDT', name: 'Tether', current_price: 1.00, price_change_percentage_24h: 0.1, image: '/crypto-icons/tether.png' },
    { id: 'binancecoin', symbol: 'BNB', name: 'BNB', current_price: 320, price_change_percentage_24h: -0.5, image: '/crypto-icons/binancecoin.png' },
    { id: 'solana', symbol: 'SOL', name: 'Solana', current_price: 95, price_change_percentage_24h: 3.2, image: '/crypto-icons/solana.png' },
    { id: 'xrp', symbol: 'XRP', name: 'XRP', current_price: 0.52, price_change_percentage_24h: -1.2, image: '/crypto-icons/xrp.png' },
    { id: 'dogecoin', symbol: 'DOGE', name: 'Dogecoin', current_price: 0.08, price_change_percentage_24h: 4.1, image: '/crypto-icons/dogecoin.png' },
    { id: 'cardano', symbol: 'ADA', name: 'Cardano', current_price: 0.45, price_change_percentage_24h: 1.5, image: '/crypto-icons/cardano.png' },
    { id: 'tron', symbol: 'TRX', name: 'TRON', current_price: 0.11, price_change_percentage_24h: 2.8, image: '/crypto-icons/tron.png' },
    { id: 'avalanche-2', symbol: 'AVAX', name: 'Avalanche', current_price: 28, price_change_percentage_24h: -0.8, image: '/crypto-icons/avalanche-2.png' },
  ];
}
