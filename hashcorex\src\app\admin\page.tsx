'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { UserManagement } from '@/components/admin/UserManagement';
import { KYCReview } from '@/components/admin/KYCReview';
import { DepositManagement } from '@/components/admin/DepositManagement';
import { WithdrawalManagement } from '@/components/admin/WithdrawalManagement';
import { SupportTicketManagement } from '@/components/admin/SupportTicketManagement';
import { BinaryPointsManagement } from '@/components/admin/BinaryPointsManagement';
import { ReferralCommissionTracking } from '@/components/admin/ReferralCommissionTracking';
import { SystemSettings } from '@/components/admin/SystemSettings';
import { SystemLogs } from '@/components/admin/SystemLogs';
import { EmailSettings } from '@/components/admin/EmailSettings';
import { MiningManagement } from '@/components/admin/MiningManagement';
import { AccountAudit } from '@/components/admin/AccountAudit';
import { Loading } from '@/components/ui';

export default function AdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null); // null = not checked yet
  const [checkingAdmin, setCheckingAdmin] = useState(true);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!loading && user) {
        try {
          const response = await fetch('/api/admin/check', {
            credentials: 'include',
          });

          if (response.ok) {
            const data = await response.json();
            setIsAdmin(data.isAdmin);
          } else {
            setIsAdmin(false);
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        } finally {
          setCheckingAdmin(false);
        }
      } else if (!loading && !user) {
        // If no user, stop checking admin status
        setCheckingAdmin(false);
        setIsAdmin(false);
      }
    };

    // Only check admin status if we have a user or if loading is complete
    if (!loading) {
      checkAdminStatus();
    }
  }, [user, loading]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  // Redirect if not admin (only after admin check is complete)
  useEffect(() => {
    // Only redirect if admin check is complete and user is explicitly not admin
    if (!checkingAdmin && !loading && user && isAdmin === false) {
      router.push('/dashboard');
    }
  }, [isAdmin, checkingAdmin, user, loading, router]);

  if (loading || checkingAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading admin panel..." />
      </div>
    );
  }

  if (!user || isAdmin === false) {
    return null;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard onTabChange={setActiveTab} />;
      case 'users':
        return <UserManagement />;
      case 'kyc':
        return <KYCReview />;
      case 'deposits':
        return <DepositManagement />;
      case 'withdrawals':
        return <WithdrawalManagement />;
      case 'mining-management':
        return <MiningManagement />;
      case 'account-audit':
        return <AccountAudit />;
      case 'support':
        return <SupportTicketManagement />;
      case 'binary-points':
        return <BinaryPointsManagement />;
      case 'referral-commissions':
        return <ReferralCommissionTracking />;
      case 'email-settings':
        return <EmailSettings />;
      case 'settings':
        return <SystemSettings />;
      case 'logs':
        return <SystemLogs />;
      default:
        return <AdminDashboard onTabChange={setActiveTab} />;
    }
  };

  return (
    <AdminLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </AdminLayout>
  );
}
