'use client';

import React from 'react';

export interface ClientErrorData {
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: string;
  componentStack?: string;
  errorBoundary?: string;
  userAction?: string;
  browserInfo?: any;
  additionalData?: any;
}

class ClientErrorLogger {
  private static instance: ClientErrorLogger;
  private isEnabled: boolean = true;
  private maxRetries: number = 3;
  private retryDelay: number = 1000;

  private constructor() {
    this.setupGlobalErrorHandlers();
  }

  static getInstance(): ClientErrorLogger {
    if (!ClientErrorLogger.instance) {
      ClientErrorLogger.instance = new ClientErrorLogger();
    }
    return ClientErrorLogger.instance;
  }

  /**
   * Enable or disable error logging
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Log an error to the server
   */
  async logError(
    error: Error | string,
    additionalData?: any,
    userAction?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const errorData: ClientErrorData = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        userAction,
        browserInfo: this.getBrowserInfo(),
        additionalData,
      };

      await this.sendErrorToServer(errorData);
    } catch (logError) {
      console.error('Failed to log error to server:', logError);
    }
  }

  /**
   * Log a React error boundary error
   */
  async logReactError(
    error: Error,
    errorInfo: any,
    componentStack?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const errorData: ClientErrorData = {
        message: error.message,
        stack: error.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        componentStack: componentStack || errorInfo.componentStack,
        errorBoundary: 'React Error Boundary',
        browserInfo: this.getBrowserInfo(),
        additionalData: {
          errorInfo,
          reactVersion: this.getReactVersion(),
        },
      };

      await this.sendErrorToServer(errorData);
    } catch (logError) {
      console.error('Failed to log React error to server:', logError);
    }
  }

  /**
   * Log a network/API error
   */
  async logNetworkError(
    url: string,
    method: string,
    status?: number,
    response?: any,
    userAction?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const errorData: ClientErrorData = {
        message: `Network error: ${method} ${url} ${status ? `(${status})` : ''}`,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        userAction,
        browserInfo: this.getBrowserInfo(),
        additionalData: {
          requestUrl: url,
          requestMethod: method,
          responseStatus: status,
          responseData: response,
          networkType: this.getNetworkInfo(),
        },
      };

      await this.sendErrorToServer(errorData);
    } catch (logError) {
      console.error('Failed to log network error to server:', logError);
    }
  }

  /**
   * Log a user action that resulted in an error
   */
  async logUserActionError(
    action: string,
    error: Error | string,
    context?: any
  ): Promise<void> {
    if (!this.isEnabled) return;

    await this.logError(error, { context }, action);
  }

  /**
   * Send error data to server with retry logic
   */
  private async sendErrorToServer(errorData: ClientErrorData): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch('/api/errors/log', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(errorData),
        });

        if (response.ok) {
          return; // Success
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    // All retries failed
    console.error('Failed to send error to server after retries:', lastError);
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError(
        event.error || new Error(event.message),
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
        'Global Error Handler'
      );
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        { type: 'unhandledrejection' },
        'Unhandled Promise Rejection'
      );
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        const target = event.target as HTMLElement;
        this.logError(
          new Error(`Resource loading error: ${target.tagName}`),
          {
            tagName: target.tagName,
            src: (target as any).src || (target as any).href,
            type: 'resource_error',
          },
          'Resource Loading Error'
        );
      }
    }, true);
  }

  /**
   * Get browser information
   */
  private getBrowserInfo(): any {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      url: window.location.href,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get network information if available
   */
  private getNetworkInfo(): any {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    
    return null;
  }

  /**
   * Get React version if available
   */
  private getReactVersion(): string | null {
    try {
      // Try to get React version from window object
      const react = (window as any).React;
      return react?.version || null;
    } catch {
      return null;
    }
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const clientErrorLogger = ClientErrorLogger.getInstance();

// Export React Error Boundary component
export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    clientErrorLogger.logReactError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />;
      }
      
      return (
        <div className="error-boundary p-4 bg-red-50 border border-red-200 rounded-lg">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Something went wrong</h2>
          <p className="text-red-600">An error occurred while rendering this component.</p>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Export utility functions
export const logError = (error: Error | string, additionalData?: any, userAction?: string) => {
  return clientErrorLogger.logError(error, additionalData, userAction);
};

export const logNetworkError = (url: string, method: string, status?: number, response?: any, userAction?: string) => {
  return clientErrorLogger.logNetworkError(url, method, status, response, userAction);
};

export const logUserActionError = (action: string, error: Error | string, context?: any) => {
  return clientErrorLogger.logUserActionError(action, error, context);
};
