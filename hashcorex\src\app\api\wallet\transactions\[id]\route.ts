import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch detailed transaction information for modal display
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { id: transactionId } = await params;

    // Get the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { 
        id: transactionId,
        userId: user.id // Ensure user can only access their own transactions
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    let additionalDetails: any = {};

    // Get additional details based on transaction type
    if (transaction.type === 'WITHDRAWAL' && transaction.reference) {
      // Get withdrawal request details
      const withdrawalRequest = await prisma.withdrawalRequest.findUnique({
        where: { id: transaction.reference }
      });

      if (withdrawalRequest) {
        additionalDetails = {
          txid: withdrawalRequest.txid,
          usdtAddress: withdrawalRequest.usdtAddress,
          rejectionReason: withdrawalRequest.rejectionReason,
          processedAt: withdrawalRequest.processedAt,
          withdrawalStatus: withdrawalRequest.status,
        };
      }
    } else if (transaction.type === 'DEPOSIT') {
      // Extract transaction ID from description and get deposit details
      const txidMatch = transaction.description.match(/TX: ([a-fA-F0-9]+)/);
      if (txidMatch) {
        const depositTxId = txidMatch[1];
        const depositRecord = await prisma.depositTransaction.findUnique({
          where: { transactionId: depositTxId }
        });

        if (depositRecord) {
          additionalDetails = {
            txid: depositRecord.transactionId,
            usdtAddress: depositRecord.tronAddress,
            confirmations: depositRecord.confirmations,
            blockNumber: depositRecord.blockNumber,
            senderAddress: depositRecord.senderAddress,
            depositStatus: depositRecord.status,
          };
        }
      }
    }

    // Return transaction with additional details
    const detailedTransaction = {
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      description: transaction.description,
      status: transaction.status,
      reference: transaction.reference,
      createdAt: transaction.createdAt,
      ...additionalDetails
    };

    return NextResponse.json({
      success: true,
      data: detailedTransaction,
    });

  } catch (error: any) {
    console.error('Transaction details fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch transaction details' },
      { status: 500 }
    );
  }
}
