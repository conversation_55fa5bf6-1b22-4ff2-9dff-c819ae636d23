'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { User } from 'lucide-react';

interface ProfileImageProps {
  src?: string | null;
  alt?: string;
  size?: number;
  className?: string;
  fallbackText?: string;
  fallbackBgColor?: string;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
}

export const ProfileImage: React.FC<ProfileImageProps> = ({
  src,
  alt = 'Profile',
  size = 40,
  className = '',
  fallbackText,
  fallbackBgColor = 'bg-gray-500',
  priority = false,
  loading = 'lazy',
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const showImage = src && !imageError;
  const showFallback = !src || imageError;

  return (
    <div 
      className={`relative flex items-center justify-center overflow-hidden ${fallbackBgColor} ${className}`}
      style={{ width: size, height: size }}
    >
      {/* Loading state */}
      {imageLoading && src && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
        </div>
      )}

      {/* Profile Image */}
      {showImage && (
        <Image
          src={src}
          alt={alt}
          width={size}
          height={size}
          className="w-full h-full object-cover"
          priority={priority}
          loading={loading}
          onError={handleImageError}
          onLoad={handleImageLoad}
          style={{ 
            opacity: imageLoading ? 0 : 1,
            transition: 'opacity 0.2s ease-in-out'
          }}
        />
      )}

      {/* Fallback */}
      {showFallback && (
        <div className="flex items-center justify-center w-full h-full">
          {fallbackText ? (
            <span className="text-white font-semibold" style={{ fontSize: size * 0.4 }}>
              {fallbackText}
            </span>
          ) : (
            <User className="text-white" style={{ width: size * 0.5, height: size * 0.5 }} />
          )}
        </div>
      )}
    </div>
  );
};
