'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';
import { Container, Grid, GridItem, Flex, PublicLayout } from '@/components/layout';
import { SolarPanel, MiningRig, Cryptocurrency, Leaf } from '@/components/icons';
import { ArrowRight, Shield, DollarSign, Users, Zap, Play, ChevronDown, Star, TrendingUp } from 'lucide-react';

export const HomePage: React.FC = () => {
  return (
    <PublicLayout>

      {/* Premium Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-yellow-50 opacity-30"></div>
        <div className="absolute inset-0 bg-white/95"></div>

        {/* Floating Elements - Hidden on mobile */}
        <div className="hidden md:block absolute top-32 left-10 w-20 h-20 bg-yellow-400/20 rounded-full animate-float"></div>
        <div className="hidden md:block absolute top-48 right-20 w-16 h-16 bg-emerald-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="hidden md:block absolute bottom-40 left-20 w-12 h-12 bg-slate-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>

        {/* Animated Icons - Hidden on mobile */}
        <div className="hidden lg:block absolute top-40 left-16 animate-float">
          <SolarPanel className="h-16 w-16 text-yellow-400/60" />
        </div>
        <div className="hidden lg:block absolute top-60 right-24 animate-float" style={{animationDelay: '1s'}}>
          <MiningRig className="h-12 w-12 text-emerald-400/60" />
        </div>
        <div className="hidden lg:block absolute bottom-32 left-1/4 animate-float" style={{animationDelay: '3s'}}>
          <Cryptocurrency className="h-14 w-14 text-slate-400/60" />
        </div>

        <Container className="relative z-10">
          <div className="text-center space-y-8 md:space-y-12 max-w-6xl mx-auto px-4">
            {/* Main Heading */}
            <div className="space-y-4 md:space-y-6">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-black text-dark-900 leading-tight">
                <span className="block">Solar-Powered</span>
                <span className="block text-yellow-500">
                  Cloud Mining
                </span>
              </h1>
              <div className="w-24 h-1.5 md:w-40 md:h-2 bg-yellow-500 mx-auto rounded-full"></div>
            </div>

            {/* Premium Subtitle */}
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium px-4">
              Join the future of sustainable cryptocurrency mining with our
              <span className="text-emerald-600 font-bold"> eco-friendly</span>, solar-powered data centers.
              <span className="text-yellow-600 font-bold"> Earn daily returns</span> while supporting renewable energy.
            </p>

            {/* Premium CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 md:gap-8 justify-center items-center pt-4 md:pt-8 px-4">
              <Link href="/register" className="w-full sm:w-auto">
                <Button variant="primary" size="xl" className="group relative overflow-hidden w-full sm:min-w-[280px]">
                  <span className="relative z-10 flex items-center justify-center gap-2 md:gap-3">
                    <SolarPanel className="w-5 h-5 md:w-6 md:h-6" />
                    <span className="text-sm md:text-base">Start Mining Today</span>
                    <ArrowRight className="w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </Button>
              </Link>

              <Link href="/how-it-works" className="w-full sm:w-auto">
                <Button variant="glass" size="xl" className="w-full sm:min-w-[280px]">
                  <Play className="w-4 h-4 md:w-5 md:h-5 mr-2 md:mr-3" />
                  <span className="text-sm md:text-base">Watch Demo</span>
                </Button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="pt-8 md:pt-16 grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 px-4">
              <div className="space-y-2 md:space-y-3 p-4 md:p-6 glass-morphism rounded-xl md:rounded-2xl">
                <div className="text-2xl md:text-4xl font-black text-solar-600">100%</div>
                <div className="text-xs md:text-sm text-gray-600 font-semibold">Solar Powered</div>
              </div>
              <div className="space-y-2 md:space-y-3 p-4 md:p-6 glass-morphism rounded-xl md:rounded-2xl">
                <div className="text-2xl md:text-4xl font-black text-eco-600">24/7</div>
                <div className="text-xs md:text-sm text-gray-600 font-semibold">Mining Active</div>
              </div>
              <div className="space-y-2 md:space-y-3 p-4 md:p-6 glass-morphism rounded-xl md:rounded-2xl">
                <div className="text-2xl md:text-4xl font-black text-purple-600">0.3-0.7%</div>
                <div className="text-xs md:text-sm text-gray-600 font-semibold">Daily ROI</div>
              </div>
              <div className="space-y-2 md:space-y-3 p-4 md:p-6 glass-morphism rounded-xl md:rounded-2xl">
                <div className="text-2xl md:text-4xl font-black text-blue-600">$50</div>
                <div className="text-xs md:text-sm text-gray-600 font-semibold">Min Investment</div>
              </div>
            </div>
          </div>
        </Container>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-8 h-8 text-gray-400" />
        </div>
      </section>

      {/* Premium Features Section */}
      <section className="py-16 md:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <Container className="relative z-10">
          <div className="text-center mb-12 md:mb-20 px-4">
            <div className="inline-flex items-center gap-2 bg-yellow-100 text-yellow-700 px-3 md:px-4 py-2 rounded-full text-xs md:text-sm font-semibold mb-4 md:mb-6">
              <Star className="w-3 h-3 md:w-4 md:h-4" />
              Premium Features
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-slate-900 mb-4 md:mb-6">
              Why Choose{' '}
              <span className="text-yellow-500">
                HashCoreX?
              </span>
            </h2>
            <p className="text-lg md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
              Experience the perfect blend of profitability and sustainability
              with our cutting-edge mining platform.
            </p>
          </div>

          <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6} className="px-4">
            <div className="group text-center p-6 md:p-8 glass-morphism rounded-2xl md:rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 bg-yellow-500 rounded-xl md:rounded-2xl mb-4 md:mb-6 group-hover:scale-110 transition-transform">
                <Shield className="h-8 w-8 md:h-10 md:w-10 text-white" />
                <div className="absolute inset-0 bg-yellow-400 rounded-xl md:rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-lg md:text-2xl font-bold text-slate-900 mb-3 md:mb-4">KYC Verified</h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                Secure and compliant platform with full KYC verification for all users.
              </p>
            </div>

            <div className="group text-center p-6 md:p-8 glass-morphism rounded-2xl md:rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 bg-emerald-500 rounded-xl md:rounded-2xl mb-4 md:mb-6 group-hover:scale-110 transition-transform">
                <DollarSign className="h-8 w-8 md:h-10 md:w-10 text-white" />
                <div className="absolute inset-0 bg-emerald-400 rounded-xl md:rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-lg md:text-2xl font-bold text-slate-900 mb-3 md:mb-4">USDT Payments</h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                Easy deposits and withdrawals using USDT (TRC20) for global accessibility.
              </p>
            </div>

            <div className="group text-center p-6 md:p-8 glass-morphism rounded-2xl md:rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 bg-slate-600 rounded-xl md:rounded-2xl mb-4 md:mb-6 group-hover:scale-110 transition-transform">
                <TrendingUp className="h-8 w-8 md:h-10 md:w-10 text-white" />
                <div className="absolute inset-0 bg-slate-400 rounded-xl md:rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-lg md:text-2xl font-bold text-slate-900 mb-3 md:mb-4">Real ROI</h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                Transparent daily returns with real mining operations and live statistics.
              </p>
            </div>

            <div className="group text-center p-6 md:p-8 glass-morphism rounded-2xl md:rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 bg-emerald-600 rounded-xl md:rounded-2xl mb-4 md:mb-6 group-hover:scale-110 transition-transform">
                <Leaf className="h-8 w-8 md:h-10 md:w-10 text-white" />
                <div className="absolute inset-0 bg-emerald-400 rounded-xl md:rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-lg md:text-2xl font-bold text-slate-900 mb-3 md:mb-4">Eco Mining</h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                100% solar-powered mining operations supporting sustainable cryptocurrency.
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-12 md:py-20 bg-dark-900 text-white">
        <Container>
          <Grid cols={{ default: 1, md: 3 }} gap={6} className="px-4">
            <div className="text-center py-6">
              <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-yellow-400 mb-2">
                1,250+
              </div>
              <div className="text-lg md:text-xl text-gray-300">TH/s Sold</div>
            </div>
            <div className="text-center py-6">
              <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-emerald-400 mb-2">
                5,000+
              </div>
              <div className="text-lg md:text-xl text-gray-300">Active Users</div>
            </div>
            <div className="text-center py-6">
              <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-yellow-400 mb-2">
                98.5%
              </div>
              <div className="text-lg md:text-xl text-gray-300">Uptime</div>
            </div>
          </Grid>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-20 bg-yellow-500 text-white">
        <Container>
          <div className="text-center max-w-3xl mx-auto px-4">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
              Ready to Start Mining?
            </h2>
            <p className="text-lg md:text-xl mb-6 md:mb-8 opacity-90 leading-relaxed">
              Join thousands of investors earning daily returns with our
              sustainable mining platform. Get started in minutes.
            </p>
            <Link href="/register">
              <Button
                size="xl"
                variant="secondary"
                className="bg-white text-dark-900 hover:bg-gray-100 w-full sm:w-auto"
              >
                <span className="text-sm md:text-base">Create Account Now</span>
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </Link>
          </div>
        </Container>
      </section>

    </PublicLayout>
  );
};
