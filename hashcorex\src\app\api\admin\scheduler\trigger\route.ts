import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { schedulerService } from '@/lib/schedulerService';
import { systemLogDb } from '@/lib/database';

// POST - Manually trigger a scheduled task (admin only)
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { taskName } = await request.json();

    if (!taskName) {
      return NextResponse.json(
        { success: false, error: 'Task name is required' },
        { status: 400 }
      );
    }

    // Validate task name
    const validTasks = ['daily-roi', 'weekly-payout', 'binary-matching', 'deposit-processing'];
    if (!validTasks.includes(taskName)) {
      return NextResponse.json(
        { success: false, error: `Invalid task name. Valid tasks: ${validTasks.join(', ')}` },
        { status: 400 }
      );
    }

    // Log the manual trigger
    await systemLogDb.create({
      action: 'MANUAL_TASK_TRIGGER',
      adminId: user.id,
      details: {
        taskName,
        triggeredBy: user.email,
        triggeredAt: new Date().toISOString(),
      },
    });

    // Trigger the task
    await schedulerService.triggerTask(taskName);

    return NextResponse.json({
      success: true,
      message: `Task '${taskName}' triggered successfully`,
      data: {
        taskName,
        triggeredBy: user.email,
        triggeredAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error triggering scheduled task:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to trigger task'
      },
      { status: 500 }
    );
  }
}
