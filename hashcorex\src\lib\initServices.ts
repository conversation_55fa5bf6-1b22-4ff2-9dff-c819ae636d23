import { depositVerificationService } from './depositVerificationService';
import { schedulerService } from './schedulerService';

/**
 * Initialize all background services
 */
export async function initializeServices() {
  try {
    console.log('Initializing background services...');

    // Start deposit verification service
    await depositVerificationService.start();

    // Start server-side scheduler service (replaces cron jobs)
    await schedulerService.initialize();

    console.log('All background services initialized successfully');
  } catch (error) {
    console.error('Error initializing background services:', error);
    throw error;
  }
}

/**
 * Shutdown all background services
 */
export function shutdownServices() {
  try {
    console.log('Shutting down background services...');
    
    // Stop deposit verification service
    depositVerificationService.stop();
    
    console.log('All background services shut down successfully');
  } catch (error) {
    console.error('Error shutting down background services:', error);
  }
}
