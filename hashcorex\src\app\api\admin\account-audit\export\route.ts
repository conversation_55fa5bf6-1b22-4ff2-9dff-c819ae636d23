import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ErrorLogger } from '@/lib/errorLogger';

// GET - Export account audit data as CSV
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = new Date(startDate);
    }
    if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      dateFilter.lte = endDateTime;
    }

    if (userId) {
      // Export detailed transactions for a specific user
      const userWithTransactions = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          transactions: {
            where: Object.keys(dateFilter).length > 0 ? {
              createdAt: dateFilter
            } : undefined,
            orderBy: { createdAt: 'asc' }
          },
          walletBalance: true,
        }
      });

      if (!userWithTransactions) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        );
      }

      // Generate detailed transaction report
      let csvContent = 'Date,Transaction ID,Type,Description,Amount,Status,Running Balance,Notes\n';
      
      let runningBalance = 0;
      const transactions = userWithTransactions.transactions;

      // Add header information
      csvContent += `# Account Audit Report for ${userWithTransactions.firstName} ${userWithTransactions.lastName}\n`;
      csvContent += `# Email: ${userWithTransactions.email}\n`;
      csvContent += `# Referral ID: ${userWithTransactions.referralId}\n`;
      csvContent += `# Report Generated: ${new Date().toISOString()}\n`;
      csvContent += `# Period: ${startDate || 'All time'} to ${endDate || 'Present'}\n`;
      csvContent += `# Current Wallet Balance: $${userWithTransactions.walletBalance?.balance || 0}\n`;
      csvContent += '\n';
      csvContent += 'Date,Transaction ID,Type,Description,Amount,Status,Running Balance,Notes\n';

      transactions.forEach(transaction => {
        const date = new Date(transaction.createdAt).toISOString().split('T')[0];
        const time = new Date(transaction.createdAt).toTimeString().split(' ')[0];
        const amount = transaction.amount;
        
        // Calculate running balance
        if (transaction.status === 'COMPLETED') {
          if (['DEPOSIT', 'MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS', 'ADMIN_CREDIT'].includes(transaction.type)) {
            runningBalance += amount;
          } else if (['WITHDRAWAL', 'PURCHASE', 'ADMIN_DEBIT'].includes(transaction.type)) {
            runningBalance -= Math.abs(amount); // Use absolute value for debits
          }
        }

        const notes = [];
        if (transaction.type === 'DEPOSIT') notes.push('Deposit to wallet');
        if (transaction.type === 'WITHDRAWAL') notes.push('Withdrawal from wallet');
        if (transaction.type === 'MINING_EARNINGS') notes.push('Mining earnings payout');
        if (transaction.type === 'DIRECT_REFERRAL') notes.push('Direct referral commission');
        if (transaction.type === 'BINARY_BONUS') notes.push('Binary bonus payout');
        if (transaction.type === 'PURCHASE') notes.push('Mining unit purchase');

        csvContent += `"${date} ${time}","${transaction.id}","${transaction.type}","${transaction.description || ''}","${amount}","${transaction.status}","${runningBalance.toFixed(2)}","${notes.join('; ')}"\n`;
      });

      // Add summary
      const deposits = transactions.filter(t => t.type === 'DEPOSIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
      const withdrawals = transactions.filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
      const earnings = transactions.filter(t => ['MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS'].includes(t.type) && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
      const purchases = transactions.filter(t => t.type === 'PURCHASE' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
      const adminCredits = transactions.filter(t => t.type === 'ADMIN_CREDIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
      const adminDebits = transactions.filter(t => t.type === 'ADMIN_DEBIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + Math.abs(t.amount), 0);
      const calculatedBalance = deposits + earnings + adminCredits - withdrawals - purchases - adminDebits;
      const currentBalance = userWithTransactions.walletBalance?.availableBalance || 0;
      const mismatch = currentBalance - calculatedBalance;

      csvContent += '\n# SUMMARY\n';
      csvContent += `"Total Deposits","","","","${deposits}","","",""\n`;
      csvContent += `"Total Withdrawals","","","","${withdrawals}","","",""\n`;
      csvContent += `"Total Earnings","","","","${earnings}","","",""\n`;
      csvContent += `"Total Purchases","","","","${purchases}","","",""\n`;
      csvContent += `"Admin Credits","","","","${adminCredits}","","",""\n`;
      csvContent += `"Admin Debits","","","","${adminDebits}","","",""\n`;
      csvContent += `"Calculated Balance","","","","${calculatedBalance.toFixed(2)}","","",""\n`;
      csvContent += `"Current Balance","","","","${currentBalance.toFixed(2)}","","",""\n`;
      csvContent += `"Balance Mismatch","","","","${mismatch.toFixed(2)}","","","${Math.abs(mismatch) >= 0.01 ? 'DISCREPANCY DETECTED' : 'BALANCED'}"\n`;

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="user-audit-${userId}-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });

    } else {
      // Export summary for all users
      const users = await prisma.user.findMany({
        include: {
          transactions: {
            where: Object.keys(dateFilter).length > 0 ? {
              createdAt: dateFilter
            } : undefined,
          },
          walletBalance: true,
        },
        orderBy: { createdAt: 'desc' }
      });

      let csvContent = 'User ID,Name,Email,Referral ID,Current Balance,Calculated Balance,Balance Mismatch,Total Deposits,Total Withdrawals,Total Earnings,Transaction Count,Has Discrepancies,Last Activity\n';

      users.forEach(user => {
        const transactions = user.transactions;

        const deposits = transactions.filter(t => t.type === 'DEPOSIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
        const withdrawals = transactions.filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
        const earnings = transactions.filter(t => ['MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS'].includes(t.type) && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
        const purchases = transactions.filter(t => t.type === 'PURCHASE' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
        const adminCredits = transactions.filter(t => t.type === 'ADMIN_CREDIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + t.amount, 0);
        const adminDebits = transactions.filter(t => t.type === 'ADMIN_DEBIT' && t.status === 'COMPLETED').reduce((sum, t) => sum + Math.abs(t.amount), 0);

        const calculatedBalance = deposits + earnings + adminCredits - withdrawals - purchases - adminDebits;
        const currentBalance = user.walletBalance?.availableBalance || 0;
        const balanceMismatch = currentBalance - calculatedBalance;
        const hasDiscrepancies = Math.abs(balanceMismatch) >= 0.01;
        const lastActivity = transactions.length > 0 ? transactions[0].createdAt.toISOString() : user.createdAt.toISOString();

        csvContent += `"${user.id}","${user.firstName} ${user.lastName}","${user.email}","${user.referralId}","${currentBalance.toFixed(2)}","${calculatedBalance.toFixed(2)}","${balanceMismatch.toFixed(2)}","${deposits.toFixed(2)}","${withdrawals.toFixed(2)}","${earnings.toFixed(2)}","${transactions.length}","${hasDiscrepancies ? 'YES' : 'NO'}","${lastActivity}"\n`;
      });

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="account-audit-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

  } catch (error: any) {
    console.error('Account audit export API error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'ACCOUNT_AUDIT_EXPORT_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Failed to export audit data' },
      { status: 500 }
    );
  }
}
