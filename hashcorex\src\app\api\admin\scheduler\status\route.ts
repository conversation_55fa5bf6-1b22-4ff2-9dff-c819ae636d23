import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { schedulerService } from '@/lib/schedulerService';
import { depositVerificationService } from '@/lib/depositVerificationService';
import { getDepositProcessingStatus } from '@/lib/depositProcessing';

// GET - Get scheduler service status (admin only)
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get scheduler status
    const schedulerStatus = schedulerService.getStatus();
    
    // Get deposit verification service status
    const depositServiceStatus = depositVerificationService.getStatus();
    
    // Get deposit processing status
    const depositProcessingStatus = await getDepositProcessingStatus();

    return NextResponse.json({
      success: true,
      data: {
        scheduler: schedulerStatus,
        depositVerificationService: depositServiceStatus,
        depositProcessing: depositProcessingStatus,
        serverTime: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error getting scheduler status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get scheduler status' },
      { status: 500 }
    );
  }
}
