import { NextRequest, NextResponse } from 'next/server';
import { schedulerService } from '@/lib/schedulerService';
import { systemLogDb } from '@/lib/database';

// DEPRECATED: This endpoint is deprecated. Daily ROI is now handled by server-side scheduler.
// This route is kept for backward compatibility and will trigger the new scheduler.
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-secret';

    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('DEPRECATED: Daily ROI cron endpoint called - redirecting to scheduler service...');

    // Log the deprecated usage
    await systemLogDb.create({
      action: 'DEPRECATED_CRON_ENDPOINT_USED',
      details: {
        endpoint: '/api/cron/daily-roi',
        message: 'This endpoint is deprecated. Daily ROI is now handled by server-side scheduler.',
        timestamp: new Date().toISOString(),
      },
    });

    // Trigger the new scheduler service
    await schedulerService.triggerTask('daily-roi');

    return NextResponse.json({
      success: true,
      message: 'Daily ROI calculation triggered via scheduler service',
      deprecated: true,
      notice: 'This endpoint is deprecated. Daily ROI is now handled automatically by server-side scheduler.',
    });

  } catch (error: unknown) {
    console.error('Daily ROI cron job error:', error);

    // Log the error
    await systemLogDb.create({
      action: 'DEPRECATED_CRON_ERROR',
      details: {
        endpoint: '/api/cron/daily-roi',
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Daily ROI calculation failed' },
      { status: 500 }
    );
  }
}
