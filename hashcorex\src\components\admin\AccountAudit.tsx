'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON>itle, CardContent, Button, Input } from '@/components/ui';
import {
  BarChart3,
  Download,
  Search,
  Filter,
  Calendar,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  FileText,
  Users,
  Eye
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

interface UserAuditSummary {
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  totalDeposits: number;
  totalWithdrawals: number;
  totalEarnings: number;
  totalPurchases: number;
  adminCredits: number;
  adminDebits: number;
  currentBalance: number;
  calculatedBalance: number;
  balanceMismatch: number;
  transactionCount: number;
  lastActivity: string;
  hasDiscrepancies: boolean;
}

interface AuditStats {
  totalUsers: number;
  usersWithDiscrepancies: number;
  totalBalanceMismatch: number;
  totalTransactions: number;
  auditedUsers: number;
}

export const AccountAudit: React.FC = () => {
  const [auditData, setAuditData] = useState<UserAuditSummary[]>([]);
  const [stats, setStats] = useState<AuditStats>({
    totalUsers: 0,
    usersWithDiscrepancies: 0,
    totalBalanceMismatch: 0,
    totalTransactions: 0,
    auditedUsers: 0,
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDiscrepancies, setFilterDiscrepancies] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserAuditSummary | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: '',
  });

  useEffect(() => {
    fetchAuditData();
  }, [searchTerm, filterDiscrepancies, dateRange]);

  const fetchAuditData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (filterDiscrepancies) params.append('discrepancies', 'true');
      if (dateRange.startDate) params.append('startDate', dateRange.startDate);
      if (dateRange.endDate) params.append('endDate', dateRange.endDate);

      const response = await fetch(`/api/admin/account-audit?${params.toString()}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuditData(data.data.users || []);
          setStats(data.data.stats || stats);
        }
      }
    } catch (error) {
      console.error('Failed to fetch audit data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportAudit = async (userId?: string) => {
    try {
      setExporting(true);
      const params = new URLSearchParams();
      if (userId) params.append('userId', userId);
      if (dateRange.startDate) params.append('startDate', dateRange.startDate);
      if (dateRange.endDate) params.append('endDate', dateRange.endDate);

      const response = await fetch(`/api/admin/account-audit/export?${params.toString()}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = userId 
          ? `user-audit-${userId}-${new Date().toISOString().split('T')[0]}.csv`
          : `account-audit-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export audit data:', error);
    } finally {
      setExporting(false);
    }
  };

  const handleViewDetails = (user: UserAuditSummary) => {
    setSelectedUser(user);
    setShowDetailModal(true);
  };

  const filteredData = auditData.filter(user => {
    const matchesSearch = !searchTerm || 
      user.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.user.referralId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = !filterDiscrepancies || user.hasDiscrepancies;
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Account Audit</h1>
          <p className="text-slate-400">Monitor and audit user wallet activities and transaction balances</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => handleExportAudit()}
            disabled={exporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {exporting ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Export All
          </Button>
          <Button
            onClick={fetchAuditData}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-400">Total Users</p>
                <p className="text-2xl font-bold text-white">{stats.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-400">With Discrepancies</p>
                <p className="text-2xl font-bold text-red-400">{stats.usersWithDiscrepancies}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-400">Balance Mismatch</p>
                <p className="text-2xl font-bold text-yellow-400">{formatCurrency(Math.abs(stats.totalBalanceMismatch))}</p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-400">Total Transactions</p>
                <p className="text-2xl font-bold text-green-400">{stats.totalTransactions}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-400">Audited Users</p>
                <p className="text-2xl font-bold text-purple-400">{stats.auditedUsers}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Search Users</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Name, email, or referral ID..."
                  className="pl-10 bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Start Date</label>
              <Input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">End Date</label>
              <Input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>

            <div className="flex items-end">
              <Button
                onClick={() => setFilterDiscrepancies(!filterDiscrepancies)}
                variant={filterDiscrepancies ? "default" : "outline"}
                className={filterDiscrepancies 
                  ? "bg-red-600 hover:bg-red-700" 
                  : "border-slate-600 text-slate-300 hover:bg-slate-700"
                }
              >
                <Filter className="h-4 w-4 mr-2" />
                {filterDiscrepancies ? 'Show All' : 'Show Discrepancies'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Results Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Account Audit Results ({filteredData.length} users)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700">
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Current Balance</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Calculated Balance</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Mismatch</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Transactions</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Last Activity</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((user) => (
                    <tr key={user.userId} className="border-b border-slate-700 hover:bg-slate-700/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-white font-medium">
                            {user.user.firstName} {user.user.lastName}
                          </div>
                          <div className="text-slate-400 text-sm">{user.user.email}</div>
                          <div className="text-slate-500 text-xs">ID: {user.user.referralId}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white font-mono">
                        {formatCurrency(user.currentBalance)}
                      </td>
                      <td className="py-3 px-4 text-white font-mono">
                        {formatCurrency(user.calculatedBalance)}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`font-mono ${
                          Math.abs(user.balanceMismatch) < 0.01
                            ? 'text-green-400'
                            : 'text-red-400 font-bold'
                        }`}>
                          {user.balanceMismatch > 0 ? '+' : ''}{formatCurrency(user.balanceMismatch)}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-slate-300">
                        {user.transactionCount}
                      </td>
                      <td className="py-3 px-4 text-slate-300 text-sm">
                        {formatDateTime(user.lastActivity)}
                      </td>
                      <td className="py-3 px-4">
                        {user.hasDiscrepancies ? (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                            <AlertTriangle className="h-3 w-3 inline mr-1" />
                            Discrepancy
                          </span>
                        ) : (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            <CheckCircle className="h-3 w-3 inline mr-1" />
                            Balanced
                          </span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(user)}
                            className="border-slate-600 text-slate-300 hover:bg-slate-700"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleExportAudit(user.userId)}
                            disabled={exporting}
                            className="border-slate-600 text-slate-300 hover:bg-slate-700"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Audit Data Found</h3>
              <p className="text-slate-400">
                {searchTerm || filterDiscrepancies
                  ? 'Try adjusting your search or filter criteria.'
                  : 'No users found for audit analysis.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Modal */}
      {showDetailModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">
                  Account Audit Details - {selectedUser.user.firstName} {selectedUser.user.lastName}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDetailModal(false)}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  ×
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Card className="bg-slate-700">
                  <CardContent className="p-4">
                    <h4 className="text-lg font-medium text-white mb-3">User Information</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Name:</span>
                        <span className="text-white">{selectedUser.user.firstName} {selectedUser.user.lastName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Email:</span>
                        <span className="text-white">{selectedUser.user.email}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Referral ID:</span>
                        <span className="text-white">{selectedUser.user.referralId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">User ID:</span>
                        <span className="text-white font-mono text-xs">{selectedUser.userId}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-700">
                  <CardContent className="p-4">
                    <h4 className="text-lg font-medium text-white mb-3">Balance Analysis</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Current Balance:</span>
                        <span className="text-white font-mono">{formatCurrency(selectedUser.currentBalance)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Calculated Balance:</span>
                        <span className="text-white font-mono">{formatCurrency(selectedUser.calculatedBalance)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Mismatch:</span>
                        <span className={`font-mono font-bold ${
                          Math.abs(selectedUser.balanceMismatch) < 0.01 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {selectedUser.balanceMismatch > 0 ? '+' : ''}{formatCurrency(selectedUser.balanceMismatch)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Status:</span>
                        <span className={selectedUser.hasDiscrepancies ? 'text-red-400' : 'text-green-400'}>
                          {selectedUser.hasDiscrepancies ? 'Has Discrepancies' : 'Balanced'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="bg-slate-700">
                <CardContent className="p-4">
                  <h4 className="text-lg font-medium text-white mb-3">Transaction Summary</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div className="text-center">
                      <div className="text-xl font-bold text-green-400">{formatCurrency(selectedUser.totalDeposits)}</div>
                      <div className="text-xs text-slate-400">Deposits</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-red-400">{formatCurrency(selectedUser.totalWithdrawals)}</div>
                      <div className="text-xs text-slate-400">Withdrawals</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-blue-400">{formatCurrency(selectedUser.totalEarnings)}</div>
                      <div className="text-xs text-slate-400">Earnings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-orange-400">{formatCurrency(selectedUser.totalPurchases)}</div>
                      <div className="text-xs text-slate-400">Purchases</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-cyan-400">{formatCurrency(selectedUser.adminCredits)}</div>
                      <div className="text-xs text-slate-400">Admin Credits</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-pink-400">{formatCurrency(selectedUser.adminDebits)}</div>
                      <div className="text-xs text-slate-400">Admin Debits</div>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-slate-600">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-400">{selectedUser.transactionCount}</div>
                      <div className="text-sm text-slate-400">Total Transactions</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  onClick={() => handleExportAudit(selectedUser.userId)}
                  disabled={exporting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {exporting ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  Export User Audit
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
