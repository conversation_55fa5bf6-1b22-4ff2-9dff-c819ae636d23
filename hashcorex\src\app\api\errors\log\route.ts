import { NextRequest, NextResponse } from 'next/server';
import { <PERSON>rrorLogger } from '@/lib/errorLogger';
import { ClientErrorData } from '@/lib/clientErrorLogger';
import { authenticateRequest } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Optional authentication - log errors even for unauthenticated users
    const { user } = await authenticateRequest(request);
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.message || !body.url || !body.userAgent) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: message, url, userAgent' },
        { status: 400 }
      );
    }

    const clientErrorData: ClientErrorData = {
      message: body.message,
      stack: body.stack,
      url: body.url,
      userAgent: body.userAgent,
      userId: user?.id,
      timestamp: body.timestamp || new Date().toISOString(),
      additionalData: {
        componentStack: body.componentStack,
        errorBoundary: body.errorBoundary,
        userAction: body.userAction,
        browserInfo: body.browserInfo,
        ...body.additionalData,
      },
    };

    await ErrorLogger.logClientError(clientErrorData);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error logging client error:', error);
    
    // Don't use ErrorLogger here to avoid infinite loops
    return NextResponse.json(
      { success: false, error: 'Failed to log error' },
      { status: 500 }
    );
  }
}
