// Edge Runtime compatible JWT utilities using jose library
import { jwtVerify, SignJWT } from 'jose';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'fallback-secret-key'
);

export interface JWTPayload {
  userId: string;
  email: string;
}

// Generate JWT token (for Edge Runtime)
export const generateTokenEdge = async (payload: JWTPayload): Promise<string> => {
  const jwt = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('30d')
    .sign(JWT_SECRET);
  
  return jwt;
};

// Verify JWT token (for Edge Runtime)
export const verifyTokenEdge = async (token: string): Promise<JWTPayload | null> => {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    
    if (payload.userId && payload.email) {
      return {
        userId: payload.userId as string,
        email: payload.email as string,
      };
    }
    
    return null;
  } catch (error) {
    return null;
  }
};
