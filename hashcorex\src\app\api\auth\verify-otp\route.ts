import { NextRequest, NextResponse } from 'next/server';
import { otpDb } from '@/lib/database';
import { ErrorLogger } from '@/lib/errorLogger';

// POST - Verify OTP
export async function POST(request: NextRequest) {
  try {
    const { email, otp, purpose = 'email_verification' } = await request.json();

    if (!email || !otp) {
      return NextResponse.json(
        { success: false, error: '<PERSON><PERSON> and <PERSON>TP are required' },
        { status: 400 }
      );
    }

    // Find valid OTP
    const otpRecord = await otpDb.findValid(email, purpose);

    if (!otpRecord) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired OTP' },
        { status: 400 }
      );
    }

    // Verify OTP
    if (otpRecord.otp !== otp) {
      return NextResponse.json(
        { success: false, error: 'Invalid OTP' },
        { status: 400 }
      );
    }

    // Mark <PERSON> as verified
    await otpDb.verify(otpRecord.id);

    return NextResponse.json({
      success: true,
      message: '<PERSON><PERSON> verified successfully',
      data: {
        email,
        verified: true,
      },
    });

  } catch (error) {
    console.error('Verify OTP error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'VERIFY_OTP_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
