import { NextRequest, NextResponse } from 'next/server';
import { schedulerService } from '@/lib/schedulerService';
import { systemLogDb } from '@/lib/database';

// DEPRECATED: This endpoint is deprecated. Weekly payout is now handled by server-side scheduler.
// This route is kept for backward compatibility and will trigger the new scheduler.
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-secret';

    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('DEPRECATED: Weekly payout cron endpoint called - redirecting to scheduler service...');

    // Log the deprecated usage
    await systemLogDb.create({
      action: 'DEPRECATED_CRON_ENDPOINT_USED',
      details: {
        endpoint: '/api/cron/weekly-payout',
        message: 'This endpoint is deprecated. Weekly payout is now handled by server-side scheduler.',
        timestamp: new Date().toISOString(),
      },
    });

    // Trigger the new scheduler service
    await schedulerService.triggerTask('weekly-payout');

    return NextResponse.json({
      success: true,
      message: 'Weekly payout triggered via scheduler service',
      deprecated: true,
      notice: 'This endpoint is deprecated. Weekly payout is now handled automatically by server-side scheduler.',
    });

  } catch (error: unknown) {
    console.error('Weekly payout cron job error:', error);

    // Log the error
    await systemLogDb.create({
      action: 'DEPRECATED_CRON_ERROR',
      details: {
        endpoint: '/api/cron/weekly-payout',
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Weekly payout failed' },
      { status: 500 }
    );
  }
}
