import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyTokenEdge } from '@/lib/auth-edge';

// Define protected and auth routes
const protectedRoutes = ['/dashboard', '/admin'];
const authRoutes = ['/login', '/register'];
const publicRoutes = ['/', '/about', '/contact'];

// Cache for admin settings to avoid repeated API calls
const settingsCache = new Map<string, { value: string; timestamp: number }>();
const CACHE_DURATION = 60000; // 1 minute cache

// Helper function to get admin setting via internal API
async function getAdminSetting(request: NextRequest, key: string): Promise<string | null> {
  try {
    // Check cache first
    const cached = settingsCache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.value;
    }

    // Make internal API call
    const baseUrl = new URL(request.url).origin;
    const response = await fetch(`${baseUrl}/api/admin/settings/check?key=${key}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      const value = data.value || null;

      // Cache the result
      settingsCache.set(key, { value, timestamp: Date.now() });
      return value;
    }

    // Return default values for critical settings if API fails
    if (key === 'maintenanceMode') return 'false';
    if (key === 'registrationEnabled') return 'true';
    if (key === 'kycRequired') return 'true';
    return null;
  } catch (error) {
    console.error(`Error fetching admin setting ${key}:`, error);
    // Return default values for critical settings
    if (key === 'maintenanceMode') return 'false';
    if (key === 'registrationEnabled') return 'true';
    if (key === 'kycRequired') return 'true';
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for certain paths
  if (pathname.startsWith('/_next') ||
      pathname.startsWith('/api') ||
      pathname === '/favicon.ico' ||
      pathname.startsWith('/uploads') ||
      pathname.startsWith('/crypto-icons')) {
    return NextResponse.next();
  }

  // Check maintenance mode (except for admin routes)
  if (!pathname.startsWith('/admin')) {
    try {
      const maintenanceMode = await getAdminSetting(request, 'maintenanceMode');
      if (maintenanceMode === 'true') {
        // Create maintenance page response
        const maintenanceHtml = `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>HashCoreX - Under Maintenance</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #1a1a1a; color: white; }
              .container { max-width: 600px; margin: 0 auto; }
              .logo { font-size: 2.5em; font-weight: bold; color: #10b981; margin-bottom: 20px; }
              .message { font-size: 1.2em; margin-bottom: 30px; }
              .details { color: #888; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="logo">HashCoreX</div>
              <h1>🔧 Under Maintenance</h1>
              <p class="message">We're currently performing scheduled maintenance to improve your experience.</p>
              <p class="details">Please check back in a few minutes. Thank you for your patience!</p>
            </div>
          </body>
          </html>
        `;

        return new NextResponse(maintenanceHtml, {
          status: 503,
          headers: {
            'Content-Type': 'text/html',
            'Retry-After': '300', // Suggest retry after 5 minutes
          },
        });
      }
    } catch (error) {
      console.error('Error checking maintenance mode:', error);
      // Continue normally if database check fails
    }
  }

  // Get token from cookie
  const token = request.cookies.get('auth-token')?.value;

  // Verify token
  let isAuthenticated = false;
  let user = null;

  if (token) {
    try {
      const decoded = await verifyTokenEdge(token);
      if (decoded && decoded.userId && decoded.email) {
        isAuthenticated = true;
        user = decoded;
      }
    } catch (error) {
      // Token is invalid, remove it
      const response = NextResponse.next();
      response.cookies.delete('auth-token');
      return response;
    }
  }

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Check if the current path is an auth route (login/register)
  const isAuthRoute = authRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Check registration enabled for register route
  if (pathname.startsWith('/register')) {
    try {
      const registrationEnabled = await getAdminSetting(request, 'registrationEnabled');
      if (registrationEnabled === 'false') {
        // Redirect to login with message
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('message', 'Registration is currently disabled');
        return NextResponse.redirect(loginUrl);
      }
    } catch (error) {
      console.error('Error checking registration enabled:', error);
      // Continue normally if database check fails
    }
  }

  // If user is not authenticated and trying to access protected route
  if (!isAuthenticated && isProtectedRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user is authenticated and trying to access auth routes
  if (isAuthenticated && isAuthRoute) {
    // Check if there's a redirect parameter
    const redirectUrl = request.nextUrl.searchParams.get('redirect');
    if (redirectUrl && redirectUrl.startsWith('/')) {
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
    // Default redirect to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons).*)',
  ],
};
