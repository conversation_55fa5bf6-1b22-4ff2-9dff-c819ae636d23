import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { emailTemplateDb } from '@/lib/database';
import { ErrorLogger } from '@/lib/errorLogger';

// GET - Get all email templates
export async function GET(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all email templates
    const templates = await emailTemplateDb.findAll();

    return NextResponse.json({
      success: true,
      data: templates,
    });

  } catch (error) {
    console.error('Get email templates error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'GET_EMAIL_TEMPLATES_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new email template
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { name, subject, htmlContent, textContent } = await request.json();

    // Validation
    if (!name || !subject || !htmlContent) {
      return NextResponse.json(
        { success: false, error: 'Name, subject, and HTML content are required' },
        { status: 400 }
      );
    }

    // Check if template with same name already exists
    const existingTemplate = await emailTemplateDb.findByName(name);
    if (existingTemplate) {
      return NextResponse.json(
        { success: false, error: 'Template with this name already exists' },
        { status: 400 }
      );
    }

    // Create template
    const template = await emailTemplateDb.create({
      name,
      subject,
      htmlContent,
      textContent,
    });

    return NextResponse.json({
      success: true,
      message: 'Email template created successfully',
      data: template,
    });

  } catch (error) {
    console.error('Create email template error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'CREATE_EMAIL_TEMPLATE_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
