'use client';

import React, { useState, useEffect, useRef } from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface CryptoPrice {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  price_change_percentage_24h: number;
  image: string;
}

export const CryptoPriceSlider: React.FC = () => {
  const [cryptoPrices, setCryptoPrices] = useState<CryptoPrice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Fallback data in case API fails
  const fallbackData: CryptoPrice[] = [
    { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin', current_price: 43250.00, price_change_percentage_24h: 2.5, image: '/crypto-icons/btc.png' },
    { id: 'ethereum', symbol: 'ETH', name: 'Ethereum', current_price: 2650.00, price_change_percentage_24h: -1.2, image: '/crypto-icons/eth.png' },
    { id: 'tether', symbol: 'USDT', name: 'Tether', current_price: 1.00, price_change_percentage_24h: 0.1, image: '/crypto-icons/usdt.png' },
    { id: 'binancecoin', symbol: 'BNB', name: 'BNB', current_price: 315.50, price_change_percentage_24h: 1.8, image: '/crypto-icons/bnb.png' },
    { id: 'solana', symbol: 'SOL', name: 'Solana', current_price: 98.75, price_change_percentage_24h: 3.2, image: '/crypto-icons/sol.png' },
  ];

  // Top 25 cryptocurrencies by market cap
  const cryptoIds = [
    'bitcoin', 'ethereum', 'tether', 'binancecoin', 'solana', 'usd-coin', 'xrp', 
    'staked-ether', 'dogecoin', 'cardano', 'tron', 'avalanche-2', 'chainlink', 
    'polygon', 'wrapped-bitcoin', 'internet-computer', 'near', 'uniswap', 
    'litecoin', 'dai', 'ethereum-classic', 'stellar', 'monero', 'bitcoin-cash', 'cosmos'
  ];

  const fetchCryptoPrices = async () => {
    try {
      setError(null);

      // Try direct CoinGecko API first
      let response;
      let data;

      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

        response = await fetch(
          `https://api.coingecko.com/api/v3/simple/price?ids=${cryptoIds.join(',')}&vs_currencies=usd&include_24hr_change=true`,
          {
            headers: {
              'Accept': 'application/json',
            },
            signal: controller.signal,
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`CoinGecko API error: ${response.status}`);
        }

        data = await response.json();

        // Transform the data to match our interface
        const prices: CryptoPrice[] = cryptoIds.map(id => {
          const priceData = data[id];
          if (!priceData) return null;

          return {
            id,
            symbol: getCryptoSymbol(id),
            name: getCryptoName(id),
            current_price: priceData.usd,
            price_change_percentage_24h: priceData.usd_24h_change || 0,
            image: getCryptoImage(id),
          };
        }).filter(Boolean) as CryptoPrice[];

        setCryptoPrices(prices);
        setLoading(false);
        return;

      } catch (directApiError) {
        console.warn('Direct CoinGecko API failed, trying fallback API:', directApiError);

        // Fallback to our internal API
        const fallbackResponse = await fetch('/api/crypto/prices', {
          headers: {
            'Accept': 'application/json',
          },
        });

        if (!fallbackResponse.ok) {
          throw new Error('Both direct and fallback APIs failed');
        }

        const fallbackData = await fallbackResponse.json();

        if (fallbackData.success && fallbackData.data) {
          setCryptoPrices(fallbackData.data);
          setLoading(false);
          return;
        } else {
          throw new Error('Invalid fallback API response');
        }
      }

    } catch (error) {
      console.warn('Error fetching crypto prices, using fallback data:', error);
      setCryptoPrices(fallbackData);
      setError(null);
      setLoading(false);
    }
  };

  useEffect(() => {
    // Use fallback data immediately due to CORS issues with CoinGecko
    setCryptoPrices(fallbackData);
    setLoading(false);

    // Try to fetch real data but don't block the UI
    fetchCryptoPrices().catch(() => {
      // Silently fail and keep using fallback data
    });

    // Update prices every 5 minutes (less frequent due to API limitations)
    const interval = setInterval(() => {
      fetchCryptoPrices().catch(() => {
        // Silently fail and keep using current data
      });
    }, 300000); // 5 minutes

    return () => {
      clearInterval(interval);
    };
  }, []);

  // Auto-scroll animation
  useEffect(() => {
    if (!sliderRef.current || cryptoPrices.length === 0) return;

    const slider = sliderRef.current;
    let scrollPosition = 0;
    const scrollSpeed = 0.5; // pixels per frame (slower speed)
    const itemWidth = 280; // approximate width of each item
    const totalWidth = cryptoPrices.length * itemWidth;

    const animate = () => {
      scrollPosition += scrollSpeed;

      // Reset position when we've scrolled through all items
      if (scrollPosition >= totalWidth / 2) { // Reset at halfway point since we duplicate items
        scrollPosition = 0;
      }

      slider.scrollLeft = scrollPosition;
      requestAnimationFrame(animate);
    };

    const animationId = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationId);
  }, [cryptoPrices]);

  const formatPrice = (price: number): string => {
    if (price >= 1) {
      return `$${price.toLocaleString('en-US', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      })}`;
    } else {
      return `$${price.toFixed(6)}`;
    }
  };

  const formatPercentage = (percentage: number): string => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Live Crypto Prices</h3>
          <div className="text-sm text-gray-500">Loading...</div>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Fetching real-time crypto prices...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 rounded-2xl p-6 mb-8">
        <div className="flex items-center justify-center text-red-600">
          <span>⚠️ {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 mb-8 overflow-hidden">
      
      <div 
        ref={sliderRef}
        className="flex space-x-4 overflow-x-hidden scrollbar-hide"
        style={{ scrollBehavior: 'auto' }}
      >
        {/* Duplicate the array to create seamless loop */}
        {[...cryptoPrices, ...cryptoPrices].map((crypto, index) => (
          <div
            key={`${crypto.id}-${index}`}
            className="flex-shrink-0 bg-white rounded-xl p-4 shadow-sm border border-gray-200 min-w-[260px]"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden">
                  <img
                    src={`https://cdn.jsdelivr.net/gh/spothq/cryptocurrency-icons@master/32/color/${crypto.id}.png`}
                    alt={crypto.name}
                    className="w-8 h-8 object-contain"
                    onError={(e) => {
                      // Fallback to alternative CDN
                      const target = e.target as HTMLImageElement;
                      target.src = `https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/32/color/${crypto.symbol.toLowerCase()}.png`;
                      target.onerror = () => {
                        // Final fallback to colored circle with symbol
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                          parent.style.backgroundColor = getCryptoColor(crypto.id);
                          parent.innerHTML = `<span class="text-sm font-bold text-white">${crypto.symbol.toUpperCase()}</span>`;
                        }
                      };
                    }}
                  />
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">
                    {crypto.symbol.toUpperCase()}
                  </div>
                  <div className="text-xs text-gray-500 truncate max-w-[100px]">
                    {crypto.name}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-bold text-gray-900 text-sm">
                  {formatPrice(crypto.current_price)}
                </div>
                <div className={`flex items-center text-xs font-medium ${
                  crypto.price_change_percentage_24h >= 0 
                    ? 'text-green-600' 
                    : 'text-red-600'
                }`}>
                  {crypto.price_change_percentage_24h >= 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {formatPercentage(crypto.price_change_percentage_24h)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Helper functions to get crypto metadata
function getCryptoSymbol(id: string): string {
  const symbolMap: Record<string, string> = {
    'bitcoin': 'BTC',
    'ethereum': 'ETH',
    'tether': 'USDT',
    'binancecoin': 'BNB',
    'solana': 'SOL',
    'usd-coin': 'USDC',
    'xrp': 'XRP',
    'staked-ether': 'stETH',
    'dogecoin': 'DOGE',
    'cardano': 'ADA',
    'tron': 'TRX',
    'avalanche-2': 'AVAX',
    'chainlink': 'LINK',
    'polygon': 'MATIC',
    'wrapped-bitcoin': 'WBTC',
    'internet-computer': 'ICP',
    'near': 'NEAR',
    'uniswap': 'UNI',
    'litecoin': 'LTC',
    'dai': 'DAI',
    'ethereum-classic': 'ETC',
    'stellar': 'XLM',
    'monero': 'XMR',
    'bitcoin-cash': 'BCH',
    'cosmos': 'ATOM',
  };
  return symbolMap[id] || id.toUpperCase();
}

function getCryptoName(id: string): string {
  const nameMap: Record<string, string> = {
    'bitcoin': 'Bitcoin',
    'ethereum': 'Ethereum',
    'tether': 'Tether',
    'binancecoin': 'BNB',
    'solana': 'Solana',
    'usd-coin': 'USD Coin',
    'xrp': 'XRP',
    'staked-ether': 'Lido Staked Ether',
    'dogecoin': 'Dogecoin',
    'cardano': 'Cardano',
    'tron': 'TRON',
    'avalanche-2': 'Avalanche',
    'chainlink': 'Chainlink',
    'polygon': 'Polygon',
    'wrapped-bitcoin': 'Wrapped Bitcoin',
    'internet-computer': 'Internet Computer',
    'near': 'NEAR Protocol',
    'uniswap': 'Uniswap',
    'litecoin': 'Litecoin',
    'dai': 'Dai',
    'ethereum-classic': 'Ethereum Classic',
    'stellar': 'Stellar',
    'monero': 'Monero',
    'bitcoin-cash': 'Bitcoin Cash',
    'cosmos': 'Cosmos',
  };
  return nameMap[id] || id;
}

function getCryptoImage(id: string): string {
  return `https://cryptologos.cc/logos/${getCryptoLogoName(id)}-${getCryptoSymbol(id).toLowerCase()}-logo.png`;
}

function getCryptoLogoName(id: string): string {
  const logoNameMap: Record<string, string> = {
    'bitcoin': 'bitcoin',
    'ethereum': 'ethereum',
    'tether': 'tether',
    'binancecoin': 'bnb',
    'solana': 'solana',
    'usd-coin': 'usd-coin',
    'xrp': 'xrp',
    'staked-ether': 'ethereum', // Use ethereum logo for staked ether
    'dogecoin': 'dogecoin',
    'cardano': 'cardano',
    'tron': 'tron',
    'avalanche-2': 'avalanche',
    'chainlink': 'chainlink',
    'polygon': 'polygon',
    'wrapped-bitcoin': 'wrapped-bitcoin',
    'internet-computer': 'internet-computer',
    'near': 'near-protocol',
    'uniswap': 'uniswap',
    'litecoin': 'litecoin',
    'dai': 'multi-collateral-dai',
    'ethereum-classic': 'ethereum-classic',
    'stellar': 'stellar',
    'monero': 'monero',
    'bitcoin-cash': 'bitcoin-cash',
    'cosmos': 'cosmos',
  };
  return logoNameMap[id] || id;
}

function getCryptoColor(id: string): string {
  const colorMapping: { [key: string]: string } = {
    'bitcoin': '#F7931A',
    'ethereum': '#627EEA',
    'tether': '#26A17B',
    'binancecoin': '#F3BA2F',
    'solana': '#9945FF',
    'usd-coin': '#2775CA',
    'staked-ether': '#00D4AA',
    'xrp': '#23292F',
    'dogecoin': '#C2A633',
    'tron': '#FF060A',
    'cardano': '#0033AD',
    'avalanche-2': '#E84142',
    'chainlink': '#375BD2',
    'polygon': '#8247E5',
    'wrapped-bitcoin': '#FF6B35',
    'internet-computer': '#29ABE2',
    'near': '#00C08B',
    'uniswap': '#FF007A',
    'litecoin': '#BFBBBB',
    'dai': '#F5AC37',
    'ethereum-classic': '#328332',
    'stellar': '#7D00FF',
    'monero': '#FF6600',
    'bitcoin-cash': '#8DC351',
    'cosmos': '#2E3148',
  };
  return colorMapping[id] || '#6B7280';
}

function getCoinGeckoImageId(id: string): string {
  const imageIdMap: Record<string, string> = {
    'bitcoin': '1',
    'ethereum': '279',
    'tether': '325',
    'binancecoin': '825',
    'solana': '4128',
    'usd-coin': '6319',
    'xrp': '44',
    'staked-ether': '13442',
    'dogecoin': '5',
    'cardano': '975',
    'tron': '1094',
    'avalanche-2': '12559',
    'chainlink': '877',
    'polygon': '4713',
    'wrapped-bitcoin': '7598',
    'internet-computer': '14495',
    'near': '6535',
    'uniswap': '12504',
    'litecoin': '2',
    'dai': '8284',
    'ethereum-classic': '453',
    'stellar': '512',
    'monero': '69',
    'bitcoin-cash': '1',
    'cosmos': '5570',
  };
  return imageIdMap[id] || '1';
}
