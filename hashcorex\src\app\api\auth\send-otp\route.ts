import { NextRequest, NextResponse } from 'next/server';
import { otpDb, userDb, emailLogDb } from '@/lib/database';
import { emailService, generateOTP } from '@/lib/email';
import { ErrorLogger } from '@/lib/errorLogger';

// POST - Send OTP for email verification or password reset
export async function POST(request: NextRequest) {
  try {
    const { email, firstName, purpose = 'email_verification' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check email existence based on purpose
    const existingUser = await userDb.findByEmail(email);

    if (purpose === 'email_verification') {
      // For registration, email should not exist
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'Email is already registered' },
          { status: 400 }
        );
      }
    } else if (purpose === 'password_reset') {
      // For password reset, email must exist
      if (!existingUser) {
        return NextResponse.json(
          { success: false, error: 'Email not found. Please check your email address.' },
          { status: 400 }
        );
      }
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10); // 10 minutes expiry

    // Save OTP to database
    await otpDb.create({
      email,
      otp,
      purpose,
      expiresAt,
    });

    // Determine email subject and template based on purpose
    const emailSubject = purpose === 'password_reset'
      ? 'Password Reset - HashCoreX'
      : 'Email Verification - HashCoreX';

    const emailTemplate = purpose === 'password_reset'
      ? 'password_reset_otp'
      : 'otp_verification';

    // Create email log entry
    const emailLog = await emailLogDb.create({
      to: email,
      subject: emailSubject,
      template: emailTemplate,
      status: 'PENDING',
    });

    // Send OTP email
    try {
      const userFirstName = existingUser?.firstName || firstName || '';
      const emailSent = await emailService.sendOTPEmail(email, otp, userFirstName, purpose);
      
      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        
        return NextResponse.json({
          success: true,
          message: 'OTP sent successfully to your email',
          data: {
            email,
            expiresAt: expiresAt.toISOString(),
          },
        });
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        
        return NextResponse.json(
          { success: false, error: 'Failed to send OTP email. Please try again.' },
          { status: 500 }
        );
      }
    } catch (emailError) {
      await emailLogDb.updateStatus(emailLog.id, 'FAILED', emailError instanceof Error ? emailError.message : 'Unknown error');
      
      console.error('Email sending error:', emailError);
      return NextResponse.json(
        { success: false, error: 'Failed to send OTP email. Please check your email configuration.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Send OTP error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'SEND_OTP_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
